import { IWorkspaceContextService } from '../../../../../platform/workspace/common/workspace.js';
import { URI } from '../../../../../base/common/uri.js';
import * as path from '../../../../../base/common/path.js';

export const convertFilePathToUri = (filePath: string, workspaceService: IWorkspaceContextService) => {
	filePath = filePath.replace(/\\/g, '/');
	if (path.isAbsolute(filePath)) {
		return URI.file(filePath);
	}

	const workspaceFolders = workspaceService.getWorkspace().folders;

	if (workspaceFolders.length === 0) {
		return URI.file(filePath);
	}

	const { workspaceUri } = getWorkspaceUri(workspaceService);

	if (!workspaceUri) {
		return URI.file(filePath);
	}

	if (workspaceUri.scheme === 'vscode-remote') {
		return URI.from({
			scheme: workspaceUri.scheme,
			authority: workspaceUri.authority,
			path: path.posix.join(workspaceUri.path, filePath)
		});
	}

	return URI.file(path.join(workspaceUri.fsPath, filePath));
};

export const getWorkspaceUri = (workspaceService: IWorkspaceContextService) => {
	const workspace = workspaceService.getWorkspace();
	if (!workspace || workspace.folders.length === 0) {
		return { workspaceUri: null, workspaceName: null };
	}
	const workspaceUri = workspace.folders[0].uri;
	const workspaceName = workspace.folders[0].name;
	return { workspaceUri, workspaceName };
};

export const getRelativePath = (workspaceService: IWorkspaceContextService, fsPath: string) => {
	const { workspaceUri } = getWorkspaceUri(workspaceService);
	if (!workspaceUri) {
		return fsPath;
	}
	return path.relative(workspaceUri.fsPath, fsPath);
}


