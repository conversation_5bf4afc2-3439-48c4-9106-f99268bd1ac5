const fs = require('fs');
const path = require('path');

// 读取 package.json
const packageJsonPath = path.join(__dirname, '..', 'package.json');
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

// 获取当前版本号
const currentVersion = packageJson.productVersion;
const versionParts = currentVersion.split('.');
const lastNumber = parseInt(versionParts[2]);
versionParts[2] = (lastNumber + 1).toString();

// 更新版本号
const newVersion = versionParts.join('.');
packageJson.productVersion = newVersion;

// 写回 package.json
fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');

console.log(`Version updated from ${currentVersion} to ${newVersion}`);
