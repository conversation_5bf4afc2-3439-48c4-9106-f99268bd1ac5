import { Widget } from '../../../../../base/browser/ui/widget.js';
import { ICodeEditor, IOverlayWidget } from '../../../../../editor/browser/editorBrowser.js';
import * as dom from '../../../../../base/browser/dom.js';
import { EditorOption } from '../../../../../editor/common/config/editorOptions.js';
import { EDITOR_LEFT_MARGIN_WIDTH } from './index.js';
import { IEditCodeService } from '../editCodeService.js';

type BlankLineTipWidgetProps = {
	editor: ICodeEditor;
	lineNumber: number;
	column: number;
};

export class BlankLineTipWidget extends Widget implements IOverlayWidget {
	private readonly ID: string;
	private readonly type: string;
	private readonly _domNode: HTMLElement;
	private readonly initialLeftPx: number;

	constructor(
		private readonly props: BlankLineTipWidgetProps,
		private readonly _editCodeService: IEditCodeService,
	) {
		super();

		this.ID = `blanklinetip-${Date.now()}`;
		this.type = 'BlankLineTipZone';
		this.initialLeftPx = this.props.editor.getScrolledVisiblePosition({ lineNumber: 1, column: 1 })?.left ?? EDITOR_LEFT_MARGIN_WIDTH;

		const { container, tipText } = dom.h('div@container', [
			dom.h('span@tipText', [])
		]);

		container.style.position = 'absolute';
		container.style.zIndex = '1';
		container.style.backgroundColor = 'var(--vscode-editor-background)';
		container.style.color = 'var(--vscode-editorGhostText-foreground)';
		container.style.fontSize = '15px';
		container.style.userSelect = 'none';
		container.style.pointerEvents = 'none';
		container.style.height = `${this.props.editor.getOption(EditorOption.lineHeight)}px`;
		container.style.display = 'flex';
		container.style.alignItems = 'center';

		const tipTextStyle = {
			display: 'flex',
			alignItems: 'center',
			height: '100%'
		};

		tipText.textContent = 'Ctrl+L to Chat, Ctrl+K to Edit';
		Object.assign(tipText.style, tipTextStyle);

		this._domNode = container;

		this.props.editor.addOverlayWidget(this);
		this.updatePosition(this.props.lineNumber);

		this._register(this.props.editor.onDidScrollChange(() => this.updatePosition()));
		this._register(this.props.editor.onDidLayoutChange(() => this.updatePosition()));
	}

	private updatePosition(initialLineNumber?: number) {
		const position = this.props.editor.getPosition();
		const lineNumber = initialLineNumber || (position ? position.lineNumber : 1);
		const lineContent = this.props.editor.getModel()?.getLineContent(lineNumber);
		if (lineContent?.trim() !== '') {
			this._editCodeService.disposeTip('BlankLineTipZone');
			return;
		}

		const topPx = this.props.editor.getTopForLineNumber(lineNumber) - this.props.editor.getScrollTop();
		this._domNode.style.top = `${topPx}px`;

		// 获取当前行的缩进位置
		const model = this.props.editor.getModel();
		if (model) {
			const lineContent = model.getLineContent(lineNumber);
			const contentToColumn = position && lineNumber === position.lineNumber
				? lineContent.substring(0, position.column - 1)
				: lineContent;
			const indentation = contentToColumn.match(/^\s*/)?.[0] || '';
			const indentWidth = this.props.editor.getOption(EditorOption.fontInfo).spaceWidth * indentation.length;

			this._domNode.style.left = `${this.initialLeftPx + indentWidth + 20}px`;
		} else {
			this._domNode.style.left = `${this.initialLeftPx + 20}px`;
		}
	}

	public getId(): string {
		return this.ID;
	}

	public getType(): string {
		return this.type;
	}

	public getDomNode(): HTMLElement {
		return this._domNode;
	}

	public getPosition() {
		return null; // 返回 null 因为我们手动控制位置
	}

	public override dispose(): void {
		this.props.editor.removeOverlayWidget(this);
		super.dispose();
	}
}
