import { IRequestService } from '../../../../../../platform/request/common/request.js';
import { request } from './request.js';

function getCodeGatewayToken(requestService: IRequestService, userId: string, token: string): Promise<string> {
	return new Promise(function (resolve, reject) {
		request(requestService, {
			url: 'https://studio.zte.com.cn/code-gateway/backend/login',
			type: "GET",
			json: true,
			headers: {
				"X-Auth-Value": token,
				"X-Emp-No": userId,
				"Cookie": "ZTEDPGSSOUser=" + userId
			}
		}, function (error: any, response: any, body: any) {
			if (error) return reject(error);
			if (body && body.code === 200 && body.data) {
				resolve(body.data.token);
			} else {
				reject(body?.message);
			}
		});
	});
};

export async function getGerritHttpPassword(requestService: IRequestService, userId: string, token: string) {
	const gatewayToken = await getCodeGatewayToken(requestService, userId, token);

	return new Promise(function (resolve, reject) {
		request(requestService, {
			url: 'https://studio.zte.com.cn/code-gateway/zte/manage/api/v1/account/get/httppassword',
			type: "POST",
			json: true,
			headers: {
				"token": gatewayToken
			}
		}, function (error: any, response: any, body: any) {
			if (error) return reject(error);
			if (body && body.code === 200 && body.data) {
				resolve(body.data);
			} else {
				reject(body?.message);
			}
		});
	});
}
