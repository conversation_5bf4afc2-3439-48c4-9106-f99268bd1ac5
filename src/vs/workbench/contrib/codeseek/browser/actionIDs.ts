// Normally you'd want to put these exports in the files that register them, but if you do that you'll get an import order error if you import them in certain cases.
// (importing them runs the whole file to get the ID, causing an import error). I guess it's best practice to separate out IDs, pretty annoying...

export const order = 1;
export const CODESEEK_VIEW_CONTAINER_ID_KEY = 'workbench.codeseek.container';
export const CODESEEK_VIEW_CONTAINER_ID = `${CODESEEK_VIEW_CONTAINER_ID_KEY}.${order}.view`;
export const CODESEEK_VIEW_ID = CODESEEK_VIEW_CONTAINER_ID;

export const CODESEEK_NEW_CONTAINER_ACTION_ID = 'codeseek.newContainerAction';
export const CODESEEK_NEW_CHAT_ACTION_ID = 'codeseek.newChatAction';

export const CODESEEK_CTRL_L_ACTION_ID = 'codeseek.ctrlLAction';

export const CODESEEK_CTRL_K_ACTION_ID = 'codeseek.ctrlKAction';

export const CODESEEK_SELECTION_TIP_ACTION_ID = 'codeseek.selectionTipAction';

export const CODESEEK_OPEN_CONTAINER_ACTION_ID = 'codeseek.openContainer';

export const CODESEEK_ACCEPT_ALL_DIFFS_ACTION_ID = 'codeseek.acceptAllDiffs';

export const CODESEEK_REJECT_ALL_DIFFS_ACTION_ID = 'codeseek.rejectAllDiffs';

export const CODESEEK_ACCEPT_DIFF_ACTION_ID = 'codeseek.acceptDiff';

export const CODESEEK_REJECT_DIFF_ACTION_ID = 'codeseek.rejectDiff';

export const CODESEEK_EXPLAIN_CODE_ACTION_ID = 'codeseek.explainCode';

export const CODESEEK_CTAGS_QUERY_ACTION_ID = 'codeseek.ctagsQuery';
