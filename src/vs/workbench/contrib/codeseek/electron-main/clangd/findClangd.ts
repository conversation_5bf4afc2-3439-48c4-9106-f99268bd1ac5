import os from 'node:os';
import path from 'node:path';
import { ILogger } from '../../../../../platform/log/common/log.js';
import { fileExists } from '../utils/common.js';

enum Platform {
	Linux = 'linux',
	Mac = 'macos',
	Windows = 'windows',
}

enum Arch {
	Arm64 = 'arm64',
	Aarch64 = 'aarch64',
	X86_64 = 'x86_64',
	X64 = 'x64',
	X86 = 'x86',
}

/**
 * 获取系统平台和架构
 */
function getOSArch(): {
	platform?: Platform
	arch?: Arch
} {
	const nodePlatformToPlatform: { [key: string]: Platform } = {
		darwin: Platform.Mac,
		linux: Platform.Linux,
		win32: Platform.Windows,
	}
	const nodeMachineToArch: { [key: string]: Arch } = {
		arm64: Arch.Aarch64,
		aarch64: Arch.Aarch64,
		x86_64: Arch.X86_64,
		x64: Arch.X86_64,
		i386: Arch.X86,
		i686: Arch.X86,
	}

	let platform: Platform | undefined
	try {
		platform = nodePlatformToPlatform[os.platform()]
	} catch {
		// Ignore errors
	}

	let arch: Arch | undefined
	try {
		arch = nodeMachineToArch[os.arch()]
	} catch {
		// Ignore errors
	}

	return {
		platform,
		arch,
	}
}

/**
 * 获取 clangd 路径，如果不存在则提示用户安装
 */
export async function findClangdPath(logger: ILogger, dataFolderName: string): Promise<string | null> {
	const { platform, arch } = getOSArch();
	if (!platform || !arch) {
		logger.error('clangd', `Unsupported platform or architecture: ${os.platform()}/${os.arch()}`);
		return null;
	}

	const { clangdPath } = getClangdPathForPlatform(platform, dataFolderName);

	// 如果已存在，直接返回路径
	if (await fileExists(clangdPath)) {
		logger.debug('clangd', `Using clangd at ${clangdPath}`);
		return clangdPath;
	}

	// 提示用户安装 clangd
	logger.error('clangd', `Clangd executable not found. Please install Clangd manually.`);

	return null;
}

/**
 * 获取预期的 clangd 路径
 */
function getClangdPathForPlatform(
	platform: Platform,
	dataFolderName: string
): { clangdPath: string } {
	// 确定存放 clangd 的目录、文件名称，多平台适配
	const isWindows = platform === Platform.Windows;
	const clangdDir = isWindows
		? path.join(process.execPath, '..', 'tools', 'clangd')
		: path.join(os.homedir(), dataFolderName, 'clangd', 'bin');

	let execName = 'clangd';
	if (isWindows) {
		execName = 'clangd.exe';
	}

	return { clangdPath: path.join(clangdDir, execName) };
}
