import { createDecorator, IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';
import { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';
import { SymfRunner, SymfSearchResult, FileURI } from './codebase/symfRunner.js';
import { Disposable } from '../../../../base/common/lifecycle.js';
import { IProductService } from '../../../../platform/product/common/productService.js';
import { EnsureIndexParams, ReindexIfStaleParams, GetLiveResultsParams, DeleteIndexParams, GetIndexStatusParams, GetResultsParams } from '../common/codebaseTypes.js';
import { ICodeseekLogger } from '../common/codeseekLogService.js';

export interface ICodebaseSymfMainService {
	readonly _serviceBrand: undefined;

	ensureIndex(params: EnsureIndexParams): Promise<void>;
	getResults(params: GetResultsParams): Promise<SymfSearchResult[]>;
	getLiveResults(params: GetLiveResultsParams): Promise<SymfSearchResult[]>;
	deleteIndex(params: DeleteIndexParams): Promise<void>;
	getIndexStatus(params: GetIndexStatusParams): Promise<'unindexed' | 'indexing' | 'ready' | 'failed'>;
	reindexIfStale(params: ReindexIfStaleParams): Promise<void>;
}

export const ICodebaseSymfMainService = createDecorator<ICodebaseSymfMainService>('codebaseSymfMainService');

export class CodebaseSymfMainService extends Disposable implements ICodebaseSymfMainService {
	readonly _serviceBrand: undefined;
	private _symfRunner: SymfRunner | undefined;

	constructor(
		@IInstantiationService private readonly instantiationService: IInstantiationService,
		@ICodeseekLogger private readonly logger: ICodeseekLogger,
		@IProductService private readonly productService: IProductService
	) {
		super();
	}

	private get symfRunner(): SymfRunner {
		if (!this._symfRunner) {
			const dataFolderName = this.productService.dataFolderName;
			this._symfRunner = this.instantiationService.createInstance(SymfRunner, this.logger, dataFolderName);
		}
		return this._symfRunner;
	}

	async getResults(params: GetResultsParams): Promise<SymfSearchResult[]> {
		const resultPromises = await this.symfRunner.getResults(params.userQuery, params.keywordQuery!, [params.repoUri]);
		const results = await Promise.all(resultPromises);
		return results.flat();
	}

	async getLiveResults(params: GetLiveResultsParams): Promise<SymfSearchResult[]> {
		if (params.files.length === 0) {
			return [];
		}
		return this.symfRunner.getLiveResults(params.userQuery, params.keywordQuery, params.files, params.token);
	}

	async ensureIndex(params: EnsureIndexParams): Promise<void> {
		if (params.repoUri.scheme !== 'file') {
			throw new Error(`Invalid URI scheme: ${params.repoUri.scheme}. Expected 'file'.`);
		}
		const dataFolderName = this.productService.dataFolderName;
		return this.symfRunner.ensureIndex(params.repoUri as FileURI, dataFolderName, params.options);
	}

	async deleteIndex(params: DeleteIndexParams): Promise<void> {
		if (params.repoUri.scheme !== 'file') {
			throw new Error(`Invalid URI scheme: ${params.repoUri.scheme}. Expected 'file'.`);
		}
		return this.symfRunner.deleteIndex(params.repoUri as FileURI);
	}

	async getIndexStatus(params: GetIndexStatusParams): Promise<'unindexed' | 'indexing' | 'ready' | 'failed'> {
		if (params.repoUri.scheme !== 'file') {
			throw new Error(`Invalid URI scheme: ${params.repoUri.scheme}. Expected 'file'.`);
		}
		return this.symfRunner.getIndexStatus(params.repoUri as FileURI);
	}

	async reindexIfStale(params: ReindexIfStaleParams): Promise<void> {
		if (params.repoUri.scheme !== 'file') {
			throw new Error(`Invalid URI scheme: ${params.repoUri.scheme}. Expected 'file'.`);
		}
		return this.symfRunner.reindexIfStale(params.repoUri as FileURI);
	}
}

registerSingleton(ICodebaseSymfMainService, CodebaseSymfMainService, InstantiationType.Eager);
