
import ping from 'ping'
const checkConfig: any = {
	'inter-network-error': '*********',
	'uac-error': 'uac.zte.com.cn',
	'icenter-error': 'icenterapi.zte.com.cn',
	'rdc-error': 'studio.zte.com.cn'
};

export async function problemCheck(httpProxy: string | undefined, httpsProxy: string | undefined) {
	const results = [];

	for (const error in checkConfig) {
		const host = checkConfig[error];
		const res = await ping.promise.probe(host);
		if (!res.alive) {
			results.push(error);
		}
	}

	if (httpProxy || httpsProxy) {
		results.push('proxy-error');
	}

	if (results.length === 0) {
		results.push('no-error');
	}

	return results;
}
