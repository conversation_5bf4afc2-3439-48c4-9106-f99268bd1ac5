import { useState, useEffect, useCallback } from 'react'
import { useAccessor, useURIStreamState, useSettingsState } from '../util/services.js'
import { useRefState } from '../util/helpers.js'
import { FeatureNames, isFeatureNameDisabled } from '../../../../common/codeseekSettingsTypes.js'
import { URI } from '../../../../../../../base/common/uri.js'

enum CopyButtonState {
	Idle = 'Copy',
	Copied = 'Copied!',
	Error = 'Could not copy',
}

const COPY_FEEDBACK_TIMEOUT = 2000 // amount of time to say 'Copied!'

const CopyButton = ({ codeStr, className = '' }: { codeStr: string, className?: string }) => {
	const accessor = useAccessor()

	const metricsService = accessor.get('IMetricsService')
	const clipboardService = accessor.get('IClipboardService')
	const [copyButtonState, setCopyButtonState] = useState<CopyButtonState>(CopyButtonState.Idle)

	useEffect(() => {
		if (copyButtonState === CopyButtonState.Idle) return
		setTimeout(() => {
			setCopyButtonState(CopyButtonState.Idle)
		}, COPY_FEEDBACK_TIMEOUT)
	}, [copyButtonState])

	const onCopy = useCallback(() => {
		clipboardService.writeText(codeStr)
			.then(() => { setCopyButtonState(CopyButtonState.Copied) })
			.catch(() => { setCopyButtonState(CopyButtonState.Error) })
		// metricsService.capture('Copy Code', { length: codeStr.length }) // capture the length only
	}, [metricsService, clipboardService, codeStr, setCopyButtonState])

	// 根据状态确定使用哪个图标
	const getIconClass = () => {
		switch (copyButtonState) {
			case CopyButtonState.Copied:
				return 'codicon codicon-check';
			case CopyButtonState.Error:
				return 'codicon codicon-run-errors';
			default:
				return 'codicon codicon-copy';
		}
	};

	return <button
		className={`flex items-center justify-center h-6 scale-75 ${className}`}
		onClick={onCopy}
		title={copyButtonState}
	>
		<span className={getIconClass()}></span>
	</button>
}





// state persisted for duration of react only
const applyingURIOfApplyBoxIdRef: { current: { [applyBoxId: string]: URI | undefined } } = { current: {} }



export const ApplyBlockHoverButtons = ({
	setApplying,
	codeStr,
	applyBoxId,
	uri,
	showCopyOnHover = false  // 默认为false，保持向后兼容
}: {
	setApplying: (applying: boolean) => void,
	codeStr: string,
	applyBoxId: string,
	uri: URI,
	showCopyOnHover?: boolean
}) => {
	const settingsState = useSettingsState()
	const isDisabled = !!isFeatureNameDisabled(FeatureNames.Apply, settingsState) || !applyBoxId

	const accessor = useAccessor()
	const editCodeService = accessor.get('IEditCodeService')
	const metricsService = accessor.get('IMetricsService')
	const commandService = accessor.get('ICommandService')


	const [_, rerender] = useState(0)

	const applyingUri = useCallback(() => applyingURIOfApplyBoxIdRef.current[applyBoxId] ?? null, [applyBoxId])
	const streamState = useCallback(() => {
		const uri = applyingUri()
		return uri ? editCodeService.getURIStreamState({ uri, applyBoxId }) : 'idle'
	}, [editCodeService, applyingUri, applyBoxId])

	// listen for stream updates
	useURIStreamState(
		useCallback((uri, newStreamState) => {
			const shouldUpdate = applyingUri()?.fsPath !== uri.fsPath
			if (shouldUpdate) return
			rerender(c => c + 1)
		}, [applyBoxId, editCodeService, applyingUri])
	)

	const onSubmit = useCallback(async () => {
		if (isDisabled) return
		const globalState = editCodeService.getURIStreamState({ uri: applyingUri()})
		if (globalState !== 'idle') return

		// 确保文件已经打开
		await editCodeService.openOrCreateFile(uri);

		// 等待编辑器准备好，检查模型是否已加载
		const waitForEditor = async () => {
			const maxAttempts = 50; // 最大等待5秒
			for (let i = 0; i < maxAttempts; i++) {
				const modelService = accessor.get('IModelService');
				const model = modelService.getModel(uri);
				if (model && !model.isDisposed()) {
					return true;
				}
				await new Promise(resolve => setTimeout(resolve, 100));
			}
			return false;
		};

		const isReady = await waitForEditor();
		if (!isReady) {
			console.warn('Editor not ready after waiting');
			return;
		}

		const newApplyingUri = await editCodeService.startApplying({
			from: 'ClickApply',
			type: 'searchReplace',
			applyStr: codeStr,
			uri,
			applyBoxId,
		})


		applyingURIOfApplyBoxIdRef.current[applyBoxId] = newApplyingUri ?? undefined
		rerender(c => c + 1)
		// metricsService.capture('Apply Code', { length: codeStr.length, uri }) // capture the length and filePath
	}, [isDisabled, streamState, editCodeService, codeStr, applyBoxId, uri, metricsService, accessor])


	const onInterrupt = useCallback(() => {
		if (streamState() !== 'streaming') return
		const uri = applyingUri()
		if (!uri) return

		editCodeService.interruptURIStreaming({ uri })
		// metricsService.capture('Stop Apply', {})
	}, [streamState, applyingUri, editCodeService, metricsService])


	const isSingleLine = !codeStr.includes('\n')

	const applyButton = <div
		className='flex items-center justify-center h-6 cursor-pointer'
		onClick={onSubmit}
	>
		<span className="codicon codicon-play scale-75"></span>
		<span className="text-xs text-codeseek-fg-1" style={{ margin: '1px 0 0 0' }}>Apply</span>
	</div>

	const stopButton = <div
		className='flex items-center justify-center h-6 cursor-pointer'
		onClick={onInterrupt}
	>
		<span className="codicon codicon-debug-stop scale-75"></span>
		<span className="text-xs text-codeseek-fg-1" style={{ margin: '1px 0 0 0' }}>Stop</span>
	</div>

	const acceptRejectButtons = <>
		<button
			// btn btn-secondary btn-sm border text-sm border-vscode-input-border rounded
			className={`${isSingleLine ? '' : 'px-1 py-0.5'} text-sm text-codeseek-fg-1`}
			onClick={() => {
				const uri = applyingUri()
				if (uri) editCodeService.removeDiffAreas({ uri, behavior: 'accept', removeCtrlKs: false })
			}}
		>
			Accept
		</button>
		<button
			// btn btn-secondary btn-sm border text-sm border-vscode-input-border rounded
			className={`${isSingleLine ? '' : 'px-1 py-0.5'} text-sm text-codeseek-fg-1`}
			onClick={() => {
				const uri = applyingUri()
				if (uri) editCodeService.removeDiffAreas({ uri, behavior: 'reject', removeCtrlKs: false })
			}}
		>
			Reject
		</button>
	</>

	const currStreamState = streamState()

	// Use useEffect to update the applying state when streamState changes
	useEffect(() => {
		setApplying(currStreamState === 'streaming')
	}, [currStreamState, setApplying])

	return <>
		{currStreamState !== 'streaming' && (
			showCopyOnHover
				? <CopyButton codeStr={codeStr} className="opacity-0 group-hover:opacity-100 transition-opacity" />
				: <CopyButton codeStr={codeStr} />
		)}
		{currStreamState === 'idle' && !isDisabled && applyButton}
		{currStreamState === 'streaming' && stopButton}
		{currStreamState === 'acceptRejectAll' && acceptRejectButtons}
	</>
}
