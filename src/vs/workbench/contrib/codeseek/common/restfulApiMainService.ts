import { Disposable } from '../../../../base/common/lifecycle.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { ILogger, LogLevel } from '../../../../platform/log/common/log.js';
import { ILoggerMainService } from '../../../../platform/log/electron-main/loggerService.js';

export interface IRestfulApiMainService {
	readonly _serviceBrand: undefined;
	initialize(): Promise<void>;
	getAllModels(): Promise<any[]>;
}

export const IRestfulApiMainService = createDecorator<IRestfulApiMainService>('restfulApiMainService');

export class RestfulApiMainService extends Disposable implements IRestfulApiMainService {
	_serviceBrand: undefined;

	// private metricsUrl = "https://wxran.zte.com.cn";
	private _logger: ILogger;

	constructor(
		@ILoggerMainService private readonly mainLogger: ILoggerMainService,
	) {
		super();
		this._logger = this.mainLogger.createLogger('restful-api-main-service');
		this._logger.setLevel(LogLevel.Debug);
	}

	async initialize(): Promise<void> {
		this._logger.info('RestfulApiMainService initialize');
	}

	async getAllModels(): Promise<any[]> {
		try {
			process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
			const headers = {
				"accept": "application/json",
				"Content-Type": "application/json"
			};
			this._logger.info(`[RestfulApiMainService] headers: ${JSON.stringify(headers)}`);
			const response = await fetch('https://wxran.zte.com.cn/ide-model-proxy/admin/configs?status=active&page=1&limit=1000', {
				method: 'GET',
				headers: headers,
			})
			const data = await response.json();
			this._logger.info(`[RestfulApiMainService] response: ${JSON.stringify(data.data)}`);
			return data.data;
		} catch (error) {
			this._logger.error(`[RestfulApiMainService] Error capturing metrics event: ${error}`);
			return [];
		}
	}
}
