/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { URI } from '../../../../base/common/uri.js';
import { Action2, registerAction2 } from '../../../../platform/actions/common/actions.js';
import { ServicesAccessor } from '../../../../editor/browser/editorExtensions.js';
import { ICodeEditorService } from '../../../../editor/browser/services/codeEditorService.js';
import { IEditorService } from '../../../services/editor/common/editorService.js';
import { IQuickInputService } from '../../../../platform/quickinput/common/quickInput.js';
import { INotificationService } from '../../../../platform/notification/common/notification.js';
import { localize2 } from '../../../../nls.js';
import { IToolsService } from '../common/toolsService.js';
import { CODESEEK_CTAGS_QUERY_ACTION_ID } from './actionIDs.js';
// import { SymbolDefinition } from '../electron-main/ctags/ctagsRunner.js';

// Register ctags query command
registerAction2(class extends Action2 {
	constructor() {
		super({
			id: CODESEEK_CTAGS_QUERY_ACTION_ID,
			title: localize2('codeseekCtagsQuery', 'Flow: Query Symbol with Ctags'),
			f1: true
		});
	}

	async run(accessor: ServicesAccessor, symbolName?: string): Promise<void> {
		const toolsService = accessor.get(IToolsService);
		const notificationService = accessor.get(INotificationService);
		const editorService = accessor.get(IEditorService);
		const quickInputService = accessor.get(IQuickInputService);

		// 如果没有提供符号名称，尝试从当前编辑器获取
		if (!symbolName) {
			symbolName = this.getSymbolFromEditor(accessor);
		}

		// 如果还是没有符号名称，提示用户输入
		if (!symbolName) {
			symbolName = await this.promptForSymbolWithService(quickInputService);
		}

		if (!symbolName) {
			return; // 用户取消了输入
		}

		try {
			const results = await toolsService.findSymbolCtagsDefinitions(symbolName);

			if (results.length === 0) {
				notificationService.info(`No definitions found for "${symbolName}"`);
				return;
			}

			if (results.length === 1) {
				// 只有一个结果，直接跳转
				await this.navigateToDefinitionWithService(editorService, results[0]);
				notificationService.info(`Found definition for "${symbolName}" in ${results[0].path}:${results[0].line}`);
			} else {
				// 多个结果，显示快速选择
				await this.showDefinitionPickerWithServices(quickInputService, editorService, symbolName, results);
			}
		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : String(error);
			notificationService.error(`Error querying symbol: ${errorMessage}`);
		}
	}

	private getSymbolFromEditor(accessor: ServicesAccessor): string | undefined {
		const codeEditorService = accessor.get(ICodeEditorService);
		const editor = codeEditorService.getActiveCodeEditor();

		if (!editor) {
			return undefined;
		}

		const selection = editor.getSelection();
		const model = editor.getModel();

		if (!selection || !model) {
			return undefined;
		}

		// 如果有选择文本，使用选择的文本
		if (!selection.isEmpty()) {
			return model.getValueInRange(selection);
		}

		// 否则获取光标位置的单词
		const wordAtPosition = model.getWordAtPosition(selection.getStartPosition());
		return wordAtPosition?.word;
	}

	private async promptForSymbolWithService(quickInputService: IQuickInputService): Promise<string | undefined> {
		return new Promise<string | undefined>((resolve) => {
			const input = quickInputService.createInputBox();
			input.placeholder = 'Enter symbol name to search';
			input.title = 'Ctags Symbol Query';

			input.onDidAccept(() => {
				const value = input.value.trim();
				input.dispose();
				resolve(value || undefined);
			});

			input.onDidHide(() => {
				input.dispose();
				resolve(undefined);
			});

			input.show();
		});
	}

	private async showDefinitionPickerWithServices(quickInputService: IQuickInputService, editorService: IEditorService, symbolName: string, results: any[]): Promise<void> {
		const items = results.map((def, index) => ({
			label: `${def.name} (${def.kind})`,
			description: `${def.path}:${def.line}`,
			detail: def.rawLineContent,
			index
		}));

		const picked = await quickInputService.pick(items, {
			title: `Found ${results.length} definitions for "${symbolName}"`,
			placeHolder: 'Select a definition to navigate to'
		});

		if (picked) {
			await this.navigateToDefinitionWithService(editorService, results[picked.index]);
		}
	}

	private async navigateToDefinitionWithService(editorService: IEditorService, definition: any): Promise<void> {
		const uri = URI.file(definition.path);

		await editorService.openEditor({
			resource: uri,
			options: {
				selection: {
					startLineNumber: definition.line,
					startColumn: 1,
					endLineNumber: definition.line,
					endColumn: 1
				}
			}
		});
	}
});
