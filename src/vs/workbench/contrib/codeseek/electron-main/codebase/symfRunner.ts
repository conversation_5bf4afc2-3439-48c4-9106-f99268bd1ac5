import * as path from 'path';
import * as os from 'os';
import { execFile as _execFile, spawn } from 'node:child_process'
import { isWindows } from '../../../../../base/common/platform.js';
import { URI } from '../../../../../base/common/uri.js';
import { getSymfPath } from './downloadSymf.js';
import { promisify } from 'util';
import fs, { access, rename, rm, writeFile } from 'node:fs/promises';
import { mkdirp } from 'mkdirp'
import { CancellationToken, CancellationTokenSource } from '../../../../../base/common/cancellation.js';
import { Mutex } from 'async-mutex';
import { IDisposable } from '../../../../../base/common/lifecycle.js';
import { Emitter } from '../../../../../base/common/event.js';
import { IndexEndEvent, IndexStartEvent } from '../../common/codebaseTypes.js';
import { ICodeseekLogger } from '../../common/codeseekLogService.js';

const execFile = promisify(_execFile);

export interface Range {
	startByte: number
	endByte: number
	startPoint: { row: number; col: number }
	endPoint: { row: number; col: number }
}

export interface SymfSearchResult {
	fqname: string
	name: string
	type: string
	doc: string
	exported: boolean
	lang: string
	file: URI
	range: Range
	summary: string
	blugeScore: number
	heuristicBoostID?: string
}

export type FileURI = Omit<URI, 'fsPath'> & {
	scheme: 'file'

	//NOTE: Re-declare this here so it doesn't pick up the @deprecated tag on URI.fsPath.
	/**
	 * The platform-specific file system path. Thank you for only using `.fsPath` on {@link FileURI}
	 * types (and not URI or URI types)! :-)
	 */
	fsPath: string
}

export interface CorpusDiff {
	maybeChangedFiles?: boolean
	changedFiles?: string[]

	// milliseconds elapsed since last index
	millisElapsed?: number

	// milliseconds of last indexing duration
	lastTimeToIndexMillis?: number
}

function parseJSONToCorpusDiff(json: string): CorpusDiff {
	const obj = JSON.parse(json)
	if (!obj.changedFiles) {
		obj.changedFiles = []
	}
	if (obj.maybeChangedFiles === undefined && obj.changedFiles === undefined) {
		throw new Error(`malformed CorpusDiff: ${json}`)
	}
	return obj as CorpusDiff
}

export function isFileURI(uri: URI): uri is FileURI {
	return uri.scheme === 'file'
}

export function assertFileURI(uri: URI): FileURI {
	if (!isFileURI(uri)) {
		throw new TypeError(`assertFileURI failed on ${uri.toString()}`)
	}
	return uri
}

function parseSymfStdout(stdout: string): SymfSearchResult[] {
	interface RawSymfResult extends Omit<SymfSearchResult, 'file'> {
		file: string
	}
	const results = JSON.parse(stdout) as RawSymfResult[]
	return results.map(result => {
		const {
			fqname,
			name,
			type,
			doc,
			exported,
			lang,
			file: fsPath,
			range,
			summary,
			blugeScore,
			heuristicBoostID,
		} = result

		const { row: startRow, col: startColumn } = range.startPoint
		const { row: endRow, col: endColumn } = range.endPoint

		const startByte = range.startByte
		const endByte = range.endByte

		return {
			fqname,
			name,
			type,
			doc,
			exported,
			lang,
			file: URI.file(fsPath),
			summary,
			range: {
				startByte,
				endByte,
				startPoint: {
					row: startRow,
					col: startColumn,
				},
				endPoint: {
					row: endRow,
					col: endColumn,
				},
			},
			blugeScore,
			heuristicBoostID,
		} satisfies SymfSearchResult
	})
}


async function fileExists(file: URI): Promise<boolean> {
	if (!isFileURI(file)) {
		throw new Error('only file URIs are supported')
	}
	try {
		await access(file.fsPath, fs.constants.F_OK)
		return true
	} catch {
		return false
	}
}

interface IndexOptions {
	retryIfLastAttemptFailed: boolean
	ignoreExisting: boolean
}

export class SymfRunner implements IDisposable {
	public dispose(): void {
		this.indexLocks.clear();
	}

	// The root of all symf index directories
	private indexRoot: FileURI
	private indexLocks: Map<string, RWLock> = new Map()

	private status: IndexStatus = new IndexStatus()

	constructor(
		private logger: ICodeseekLogger,
		private dataFolderName: string
	) {
		const globalStoragePath = path.join(os.homedir(), this.dataFolderName, 'symf', 'indexroot');
		const indexRoot = URI.file(globalStoragePath);

		if (!isFileURI(indexRoot)) {
			throw new Error('symf only supports running on the file system')
		}
		this.indexRoot = indexRoot
	}

	public onIndexStart(cb: (e: IndexStartEvent) => void): IDisposable {
		return this.status.onDidStart(cb)
	}

	public onIndexEnd(cb: (e: IndexEndEvent) => void): IDisposable {
		return this.status.onDidEnd(cb)
	}

	private async mustSymfPath(): Promise<string> {
		const symfPath = await getSymfPath(this.logger, this.dataFolderName);
		if (!symfPath) {
			throw new Error('No symf executable')
		}
		return symfPath
	}

	public getResults(userQuery: string, keywordQuery: string, repoUris: URI[]): Promise<Promise<SymfSearchResult[]>[]> {
		this.logger.debug(`userQuery: ${userQuery} repoUris: ${repoUris.map(s => s.toString()).join(', ')}`)
		return Promise.resolve(
			repoUris.filter(isFileURI).map(repoUri => this.getResultsForrepoUri(userQuery, keywordQuery, repoUri))
		)
	}

	public async getLiveResults(
		userQuery: string,
		keywordQuery: string,
		files: string[],
		token?: CancellationToken
	): Promise<SymfSearchResult[]> {
		const symfPath = await this.mustSymfPath()
		const args = [
			'live-query',
			...files.flatMap(f => ['-f', f]),
			'--limit',
			files.length < 5 ? `${files.length}` : '5',
			'--fmt',
			'json',
			'--boosted-keywords',
			`${userQuery}`,
			`${keywordQuery}`,
		]
		try {
			const { stdout } = await execFile(symfPath, args, {
				env: { HOME: process.env.HOME },
				maxBuffer: 1024 * 1024 * 1024,
				timeout: 1000 * 30, // timeout in 30 seconds
			})

			if (token?.isCancellationRequested) {
				this.logger.error('SymfRunner', 'Operation aborted')
				return []
			}

			return parseSymfStdout(stdout)
		} catch (error) {
			this.logger.error('SymfRunner', 'Error running symf live query', error)
			if (token?.isCancellationRequested) {
				this.logger.error('SymfRunner', 'Operation aborted')
			}
			return []
		}
	}

	/**
	 * Returns the list of results from symf for a single directory scope.
	 * @param keywordQuery is a promise, because query expansion might be an expensive
	 * operation that is best done concurrently with querying and (re)building the index.
	 */
	private async getResultsForrepoUri(userQuery: string, keywordQuery: string, repoUri: FileURI): Promise<SymfSearchResult[]> {
		const maxRetries = 10

		// Run in a loop in case the index is deleted before we can query it
		for (let i = 0; i < maxRetries; i++) {
			await this.getIndexLock(repoUri).withWrite(async () => {
				await this.unsafeEnsureIndex(repoUri, this.dataFolderName, {
					retryIfLastAttemptFailed: i === 0,
					ignoreExisting: false,
				})
			})

			let indexNotFound = false
			const stdout = await this.getIndexLock(repoUri).withRead(async () => {
				// Check again if index exists after we have the read lock
				if (!(await this.unsafeIndexExists(repoUri))) {
					indexNotFound = true
					return ''
				}
				return this.unsafeRunQuery(userQuery, keywordQuery, repoUri)
			})
			if (indexNotFound) {
				continue
			}
			return parseSymfStdout(stdout)
		}
		throw new Error(`failed to find index after ${maxRetries} tries for directory ${repoUri}`)
	}

	public async deleteIndex(repoUri: FileURI): Promise<void> {
		await this.getIndexLock(repoUri).withWrite(async () => {
			await this.unsafeDeleteIndex(repoUri)
		})
	}

	public async getIndexStatus(
		repoUri: FileURI
	): Promise<'unindexed' | 'indexing' | 'ready' | 'failed'> {
		if (this.status.isInProgress(repoUri)) {
			// Check this before waiting on the lock
			return 'indexing'
		}
		const hasIndex = await this.getIndexLock(repoUri).withRead(async () => {
			return this.unsafeIndexExists(repoUri)
		})
		if (hasIndex) {
			return 'ready'
		}
		if (await this.didIndexFail(repoUri)) {
			return 'failed'
		}
		return 'unindexed'
	}

	/**
	 * Check index freshness and reindex if needed.
	 */
	public async reindexIfStale(repoUri: FileURI): Promise<void> {
		this.logger.info('SymfRunner', 'reindexIfStale', repoUri.fsPath)
		try {
			const diff = await this.statIndex(repoUri)
			if (!diff) {
				await this.ensureIndex(repoUri, this.dataFolderName, {
					retryIfLastAttemptFailed: false,
					ignoreExisting: false,
				})
				return
			}

			if (shouldReindex(diff)) {
				// reindex targeting a temporary directory
				// atomically replace index
				await this.ensureIndex(repoUri, this.dataFolderName, {
					retryIfLastAttemptFailed: false,
					ignoreExisting: true,
				})
			}
		} catch (error) {
			this.logger.error('SymfRunner', `Error checking freshness of index at ${repoUri.fsPath}`, error)
		}
	}

	private async statIndex(repoUri: FileURI): Promise<CorpusDiff | null> {
		const { indexDir } = this.getIndexDir(repoUri)
		const symfPath = await this.mustSymfPath()
		const args = ['--index-root', indexDir.fsPath, 'status', repoUri.fsPath]

		this.logger.info('SymfRunner', 'statIndex', symfPath, args.join(' '))
		try {
			const { stdout } = await execFile(symfPath, args)
			return parseJSONToCorpusDiff(stdout)
		} catch (error) {
			this.logger.error('SymfRunner', 'symf status error', error)
			return null
		}
	}

	/**
	 * Triggers indexing for a repoUri.
	 *
	 * Options:
	 * - retryIfLastAttemptFailed: if the last indexing run ended in failure, we don't retry
	 *   unless this value is true.
	 * - ignoreExisting: if an index already exists, we don't reindex unless this value is true.
	 *   This should be set to true when we want to update an index because files have changed.
	 */
	public async ensureIndex(
		repoUri: FileURI,
		dataFolderName: string,
		options: IndexOptions = { retryIfLastAttemptFailed: false, ignoreExisting: false }
	): Promise<void> {
		await this.getIndexLock(repoUri).withWrite(async () => {
			await this.unsafeEnsureIndex(repoUri, dataFolderName, options)
		})
	}

	private getIndexLock(repoUri: FileURI): RWLock {
		const { indexDir } = this.getIndexDir(repoUri)
		let lock = this.indexLocks.get(indexDir.toString())
		if (lock) {
			return lock
		}
		lock = new RWLock()
		this.indexLocks.set(indexDir.toString(), lock)
		return lock
	}

	private async unsafeRunQuery(userQuery: string, keywordQuery: string, repoUri: FileURI): Promise<string> {
		const { indexDir } = this.getIndexDir(repoUri)
		const symfPath = await this.mustSymfPath()
		const symfArgs = [
			'--index-root',
			indexDir.fsPath,
			'query',
			'--scopes',
			repoUri.fsPath,
			'--fmt',
			'json',
			'--boosted-keywords',
			`"${userQuery}"`,
			`${keywordQuery}`,
		]
		this.logger.debug('SymfRunner', 'running symf', symfPath, symfArgs.join(' '))
		try {
			const { stdout } = await execFile(symfPath, symfArgs, {
				env: { HOME: process.env.HOME },
				maxBuffer: 1024 * 1024 * 1024,
				timeout: 1000 * 30, // timeout in 30 seconds
			})
			return stdout
		} catch (error) {
			throw new Error(`Symf error: ${error.message || error}`)
		}
	}

	private async unsafeDeleteIndex(repoUri: FileURI): Promise<void> {
		const trashRootDir = URI.joinPath(this.indexRoot, '.trash')
		await mkdirp(trashRootDir.fsPath)
		const { indexDir } = this.getIndexDir(repoUri)

		if (!(await fileExists(indexDir))) {
			// index directory no longer exists, nothing to do
			return
		}

		// Unique name for trash directory
		const trashDir = URI.joinPath(trashRootDir, `${path.basename(indexDir.path)}-${Date.now()}`);
		if (await fileExists(trashDir)) {
			// if trashDir already exists, error
			throw new Error(
				`could not delete index ${indexDir}: target trash directory ${trashDir} already exists`
			)
		}

		await rename(indexDir.fsPath, trashDir.fsPath)
		rm(trashDir.fsPath, { recursive: true, force: true }).catch(() => { }) // delete in background
	}

	private async unsafeIndexExists(repoUri: FileURI): Promise<boolean> {
		const { indexDir } = this.getIndexDir(repoUri)
		return fileExists(URI.joinPath(indexDir, 'index.json'))
	}

	private async unsafeEnsureIndex(repoUri: FileURI, dataFolderName: string, options: IndexOptions): Promise<void> {
		this.logger.debug('SymfRunner', 'unsafeEnsureIndex', repoUri.fsPath, { verbose: { options } })
		if (!options.ignoreExisting) {
			const indexExists = await this.unsafeIndexExists(repoUri)
			if (indexExists) {
				return
			}
		}

		if (!options.retryIfLastAttemptFailed && (await this.didIndexFail(repoUri))) {
			// Index build previous failed, so don't try to rebuild
			this.logger.debug(
				'symf',
				'index build previously failed and retryIfLastAttemptFailed=false, not rebuilding'
			)
			return
		}

		const { indexDir, tmpDir } = this.getIndexDir(repoUri)
		try {
			await this.unsafeUpsertIndex(indexDir, tmpDir, repoUri, dataFolderName)
		} catch (error) {
			this.logger.debug('symf', 'symf index creation failed', error)
			await this.markIndexFailed(repoUri)
			throw error
		}
		await this.clearIndexFailure(repoUri)
	}

	private getIndexDir(repoUri: FileURI): { indexDir: FileURI; tmpDir: FileURI } {
		let indexSubdir = repoUri.path

		// On Windows, we can't use an absolute path with a drive letter inside another path
		// so we remove the colon, so `/c:/foo/` becomes `/c/foo` and `/c%3A/foo` becomes `/c/foo`.
		if (isWindows) {
			if (indexSubdir[2] === ':') {
				indexSubdir = indexSubdir.slice(0, 2) + indexSubdir.slice(3)
			} else if (indexSubdir.slice(2, 5) === '%3A') {
				indexSubdir = indexSubdir.slice(0, 2) + indexSubdir.slice(5)
			}
		}

		return {
			indexDir: assertFileURI(URI.joinPath(this.indexRoot, indexSubdir)),
			tmpDir: assertFileURI(URI.joinPath(this.indexRoot, '.tmp', indexSubdir)),
		}
	}

	private unsafeUpsertIndex(
		indexDir: FileURI,
		tmpIndexDir: FileURI,
		repoUri: FileURI,
		dataFolderName: string
	): Promise<void> {
		const cancellation = new CancellationTokenSource()
		const upsert = this._unsafeUpsertIndex(indexDir, tmpIndexDir, repoUri, dataFolderName, cancellation.token)
		this.status.didStart({ repoUri, done: upsert, cancel: () => cancellation.cancel() })
		void upsert.finally(() => {
			this.status.didEnd({ repoUri })
			cancellation.dispose()
		})
		return upsert
	}

	private async _unsafeUpsertIndex(
		indexDir: FileURI,
		tmpIndexDir: FileURI,
		repoUri: FileURI,
		dataFolderName: string,
		cancellationToken: CancellationToken
	): Promise<void> {
		const symfPath = await getSymfPath(this.logger, this.dataFolderName);
		if (!symfPath) {
			return
		}
		await rm(tmpIndexDir.fsPath, { recursive: true }).catch(() => undefined)

		this.logger.debug('symf', 'creating index', indexDir)
		let maxCPUs = 1
		if (os.cpus().length > 4) {
			maxCPUs = 2
		}

		const disposeOnFinish: IDisposable[] = []
		if (cancellationToken.isCancellationRequested) {
			throw new AbortError()
		}

		let wasCancelled = false
		let onExit: (() => void) | undefined
		try {
			const proc = spawn(symfPath, ['--index-root', tmpIndexDir.fsPath, 'add', repoUri.fsPath], {
				env: {
					...process.env,
					GOMAXPROCS: `${maxCPUs}`, // use at most one cpu for indexing
				},
				stdio: ['ignore', 'ignore', 'ignore'],
				timeout: 1000 * 60 * 10, // timeout in 10 minutes
			})
			onExit = () => {
				proc.kill('SIGKILL')
			}
			process.on('exit', onExit)

			if (cancellationToken.isCancellationRequested) {
				wasCancelled = true;
				proc.kill('SIGKILL');
			} else {
				disposeOnFinish.push(
					cancellationToken.onCancellationRequested(() => {
						wasCancelled = true
						proc.kill('SIGKILL')
					})
				)
			}

			// wait for proc to finish
			await new Promise<void>((resolve, reject) => {
				proc.on('error', reject)
				proc.on('exit', code => {
					if (code === 0) {
						resolve()
					} else {
						reject(new Error(`symf exited with code ${code}`))
					}
				})
			})

			// move just-built index to index path
			await rm(indexDir.fsPath, { recursive: true }).catch(() => undefined)
			await mkdirp(path.dirname(indexDir.fsPath));
			await rename(tmpIndexDir.fsPath, indexDir.fsPath)
		} catch (error) {
			if (wasCancelled) {
				throw new AbortError()
			}
			throw new Error(`Symf error: ${error.message || error}`)
		} finally {
			if (onExit) {
				process.removeListener('loaded', onExit)
				disposeOnFinish.forEach(disposable => disposable.dispose());
				await rm(tmpIndexDir.fsPath, { recursive: true, force: true })
			}
		}
	}

	/**
	 * Helpers for tracking index failure
	 */
	private async markIndexFailed(repoUri: FileURI): Promise<void> {
		const failureRoot = URI.joinPath(this.indexRoot, '.failed')
		await mkdirp(failureRoot.fsPath)
		const failureSentinelFile = URI.joinPath(failureRoot, repoUri.path.replaceAll('/', '__'))
		await writeFile(failureSentinelFile.fsPath, '')
	}

	private async didIndexFail(repoUri: FileURI): Promise<boolean> {
		const failureRoot = URI.joinPath(this.indexRoot, '.failed')
		const failureSentinelFile = URI.joinPath(failureRoot, repoUri.path.replaceAll('/', '__'))
		return fileExists(failureSentinelFile)
	}

	private async clearIndexFailure(repoUri: FileURI): Promise<void> {
		const failureRoot = URI.joinPath(this.indexRoot, '.failed')
		const failureSentinelFile = URI.joinPath(failureRoot, repoUri.path.replaceAll('/', '__'))
		await rm(failureSentinelFile.fsPath, { force: true })
	}
}

class IndexStatus implements IDisposable {
	private startEmitter = new Emitter<IndexStartEvent>()
	private stopEmitter = new Emitter<IndexEndEvent>()
	private inProgressDirs = new Set<string /* uri.toString() */>()

	public dispose(): void {
		this.startEmitter.dispose()
		this.stopEmitter.dispose()
	}

	public didStart(event: IndexStartEvent): void {
		this.inProgressDirs.add(event.repoUri.toString())
		this.startEmitter.fire(event)
	}

	public didEnd(event: IndexEndEvent): void {
		this.inProgressDirs.delete(event.repoUri.toString())
		this.stopEmitter.fire(event)
	}

	public onDidStart(cb: (e: IndexStartEvent) => void): IDisposable {
		return this.startEmitter.event(cb)
	}

	public onDidEnd(cb: (e: IndexEndEvent) => void): IDisposable {
		return this.stopEmitter.event(cb)
	}

	public isInProgress(repoUri: FileURI): boolean {
		return this.inProgressDirs.has(repoUri.toString())
	}
}


/**
 * A simple read-write lock.
 *
 * Note: it is possible for an overlapping succession of readers to starve out
 * any writers that are waiting for the mutex to be released. In practice, this
 * is not an issue, because we don't expect the user to issue neverending
 * while trying to update the index.
 */
class AbortError extends Error {
	constructor() {
		super('Operation aborted');
		this.name = 'AbortError';
	}
}

class RWLock {
	/**
	 * Invariants:
	 * - if readers > 0, then mu is locked
	 * - if readers === 0 and mu is locked, then a writer is holding the lock
	 */
	private readers = 0
	private mu = new Mutex()

	public async withRead<T>(fn: () => Promise<T>): Promise<T> {
		while (this.readers === 0) {
			if (this.mu.isLocked()) {
				// If mu is locked at this point, it must be held by the writer.
				// We spin in this case, rather than try to acquire the lock,
				// because multiple readers blocked on acquiring the lock will
				// execute serially when the writer releases the lock (whereas
				// we want all reads to be concurrent).
				await new Promise(resolve => setTimeout(resolve, 100))
				continue
			}
			// No readers or writers: acquire lock for readers
			await this.mu.acquire()
			break
		}
		this.readers++
		try {
			return await fn()
		} finally {
			this.readers--
			if (this.readers === 0) {
				this.mu.release()
			}
		}
	}

	public async withWrite<T>(fn: () => Promise<T>): Promise<T> {
		return this.mu.runExclusive(fn)
	}
}

/**
 * Determines whether the search index should be refreshed based on whether we're past a staleness threshold
 * that depends on the time since last index, time it took to create the last index, and the number of files changed.
 */
export function shouldReindex(diff: CorpusDiff): boolean {
	if (diff.millisElapsed === undefined) {
		return true
	}

	const numChangedFiles = diff.changedFiles
		? diff.changedFiles.length
		: diff.maybeChangedFiles
			? 9999999
			: 0
	if (numChangedFiles === 0) {
		return false
	}

	const stalenessThresholds = [
		{
			// big change thresholds
			changedFiles: 20,
			thresholds: [
				{ lastTimeToIndexMillis: 1000 * 60 * 5, maxMillisStale: 30 * 1000 }, // 5m -> 30s
				{ lastTimeToIndexMillis: 1000 * 60 * 5, maxMillisStale: 60 * 1000 }, // 10m -> 1m
			],
		},
		{
			// small change thresholds
			changedFiles: 0,
			thresholds: [
				{ lastTimeToIndexMillis: 1000 * 30, maxMillisStale: 1000 * 60 * 5 }, // 30s -> 5m
				{ lastTimeToIndexMillis: 1000 * 60, maxMillisStale: 1000 * 60 * 15 }, // 1m -> 15m
				{ lastTimeToIndexMillis: 1000 * 60 * 5, maxMillisStale: 1000 * 60 * 60 }, // 5m -> 1h
				{ lastTimeToIndexMillis: 1000 * 60 * 10, maxMillisStale: 1000 * 60 * 60 * 2 }, // 10m -> 2h
			],
		},
	]

	const fallbackThreshold = 1000 * 60 * 60 * 24 // 1 day

	let t0 = undefined
	for (const thresh of stalenessThresholds) {
		if (numChangedFiles >= thresh.changedFiles) {
			t0 = thresh
			break
		}
	}
	if (!t0) {
		// should never happen given last changedFiles is 0
		return diff.millisElapsed >= fallbackThreshold
	}
	if (diff.lastTimeToIndexMillis === undefined) {
		return true
	}

	for (const thresh of t0.thresholds) {
		if (diff.lastTimeToIndexMillis <= thresh.lastTimeToIndexMillis) {
			return diff.millisElapsed >= thresh.maxMillisStale
		}
	}
	return diff.millisElapsed >= fallbackThreshold
}


