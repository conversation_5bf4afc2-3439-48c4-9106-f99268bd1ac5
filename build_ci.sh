#!/bin/bash

set -ex

usage() {
    echo "Usage: $0 [options]"
    echo "Options:"
    echo "  -h, --help    Show this help message and exit"
    echo "  local  Package or Test the application in local environment"
    echo "  test   Run the tests"
    echo "  pkg    Package the linux rpm application for distribution"
    echo "Example:"
    echo "  $0 test, # test in ci environment"
    echo "  $0 pkg, # package in ci environment"
    echo "  $0 local test, # test in local environment"
    echo "  $0 local pkg,  # package in local environment"
}

if [[ "$*" =~ "-h" ]] || [[ "$*" =~ "--help" ]]; then
    usage
    exit 0
fi

if [ $# -lt 1 ]; then
    echo "Error: No arguments provided."
    usage
    exit 1
fi

root_dir="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

export ELECTRON_GET_USE_PROXY=1
export ELECTRON_MIRROR="https://npmmirror.com/mirrors/electron/"

[ -d ${root_dir}/.build ] && rm -rf ${root_dir}/.build

export NPM_CONFIG_CACHE="$HOME/.npm-cache"
export npm_config_cache="$HOME/.npm-cache"

# CI 环境
if [[ ! $* =~ "local" ]]; then
	export NODE_OPTIONS="--max-old-space-size=16384"  # 16GB
	export TS_NODE_COMPILER_OPTIONS='{"maxNodeModuleJsDepth":0}'

    export PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=1
    export ELECTRON_SKIP_BINARY_DOWNLOAD=1
    export CXXFLAGS="-std=c++20"

    rm -rf $root_dir/package-lock.json
    [ -d "$root_dir/node_modules" ] && rm -rf $root_dir/node_modules

    # 为extensions/emmet安装依赖
    cd $root_dir/extensions/emmet
    npm install $HOME/.npm-packages/emmet/emmetio-css-parser-0.4.0.tgz --verbose
    # 为build提前安装tree-sitter，解决C++20协议依赖问题
    cd $root_dir/build
    npm install tree-sitter --verbose
    cd $root_dir

    npm install $HOME/.npm-packages/node-pty-1.1.0-beta22.tgz --verbose
    npm install --no-audit --prefer-offline --verbose

    [ -d $root_dir/.build/electron ] && rm -rf $root_dir/.build/electron
    cp -rf /opt/electron $root_dir/.build/
else #  本地环境
    npm install --no-audit --verbose
fi

# 编译
npm run compile

# 测试
if [[ "$*" =~ "test" ]]; then
	npm run test-node
    # npm run test-extension
    # npm run test-browser-no-install
    # npm run test-browser
fi

# 打包
if [[ "$*" =~ "pkg" ]]; then
    if  [[ ! $* =~ "local" ]]; then
        cpu_num=$(grep -c ^processor /proc/cpuinfo)
        export MAX_PARALLEL=$cpu_num

		[ -d $root_dir/.build/builtInExtensions ] && rm -rf $root_dir/.build/builtInExtensions
        cp -rf $HOME/builtInExtensions $root_dir/.build/

        # 使用Node.js脚本添加内置扩展到product.json
        node $root_dir/scripts/addBuiltInExtensions.js $root_dir

        # 启动 http server 用于下载 electron
        python3 -m http.server 6219 --directory $HOME/.cache/electron &
        export ELECTRON_MIRROR="http://localhost:6219/"
    fi
    ELECTRON_GET_USE_PROXY=1 npm run gulp vscode-linux-x64
    # 打包 rpm 和 远程服务器
	./scripts/build-remote-server.sh &
	remote_server_pid=$!
	./scripts/build-rpm.sh &
	rpm_pid=$!

	# 等待两个进程完成并检查它们的退出状态
	wait $remote_server_pid
	remote_server_status=$?
	wait $rpm_pid
	rpm_status=$?

	if [ $remote_server_status -ne 0 ] || [ $rpm_status -ne 0 ]; then
		echo "构建失败: 远程服务器构建状态 $remote_server_status, RPM构建状态 $rpm_status"
		exit 1
	fi

	echo "远程服务器和RPM包构建成功完成"
fi



