/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { Disposable } from '../../../../base/common/lifecycle.js';
import { ServicesAccessor } from '../../../../editor/browser/editorExtensions.js';
import { Action2, MenuId, registerAction2 } from '../../../../platform/actions/common/actions.js';
import { CodeseekUpdateService, FLOW_HAS_UPDATE_AVAILABLE, ICodeseekUpdateService } from '../common/codeseekUpdateService.js';
import { IWorkbenchContribution, registerWorkbenchContribution2, WorkbenchPhase } from '../../../common/contributions.js';
import * as nls from '../../../../nls.js';
import { Codicon } from '../../../../base/common/codicons.js';

export const CODESEEK_UPDATE_ACTION_ID = 'workbench.action.updateCodeseek';
registerAction2(class extends Action2 {
	constructor() {
		super({
			id: CODESEEK_UPDATE_ACTION_ID,
			title: nls.localize2('updateCodeseek', "Flow Update"),
			icon: Codicon.cloudDownload,
			menu: [
				{
					id: MenuId.LayoutControlMenu,
					group: '0_start',
					when: FLOW_HAS_UPDATE_AVAILABLE
				}
			]
		});
	}

	async run(accessor: ServicesAccessor): Promise<void> {
		try {
			const updateService = accessor.get(ICodeseekUpdateService) as CodeseekUpdateService;
			updateService.install_();
		} catch (error) { }
	}
});

// on mount
class CodeseekUpdateWorkbenchContribution extends Disposable implements IWorkbenchContribution {
	static readonly ID = 'workbench.contrib.codeseek.codeseekUpdate';

	constructor(
		@ICodeseekUpdateService private readonly codeseekUpdateService: CodeseekUpdateService,
	) {
		super();
		// 启动后延迟5秒检查更新
		const initId = setTimeout(() => this.codeseekUpdateService.update(), 5 * 1000);
		this._register({ dispose: () => clearTimeout(initId) });

		// 每3小时检查一次更新
		const intervalId = setInterval(() => this.codeseekUpdateService.update(false), 3 * 60 * 60 * 1000);
		this._register({ dispose: () => clearInterval(intervalId) });
	}
}

registerWorkbenchContribution2(CodeseekUpdateWorkbenchContribution.ID, CodeseekUpdateWorkbenchContribution, WorkbenchPhase.BlockRestore);
