import { URI } from '../../../../base/common/uri.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';
import { IWorkspaceContextService } from '../../../../platform/workspace/common/workspace.js';
import { ISearchService, resultIsMatch } from '../../../services/search/common/search.js';
import { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';
import { QueryBuilder } from '../../../services/search/common/queryBuilder.js';
import { Disposable } from '../../../../base/common/lifecycle.js';
import { ICodeseekLogger } from '../common/codeseekLogService.js';
import { ICodebaseService, ISearchResult } from '../common/codebaseTypes.js';
import { ICodebaseRemoteService } from '../common/codebaseRemoteService.js';

export interface ICodebaseSearchService {
	readonly _serviceBrand: undefined;
	// 语义搜索
	semanticSearch(query: string, maxResults?: number): Promise<ISearchResult[]>;
	// 文本搜索
	textSearch(query: string, includePattern?: string, excludePattern?: string): Promise<ISearchResult[]>;
	// 文件搜索
	fileSearch(pattern: string): Promise<URI[]>;
}

export const ICodebaseSearchService = createDecorator<ICodebaseSearchService>('codebaseSearchService');

export class CodebaseSearchService extends Disposable implements ICodebaseSearchService {
	readonly _serviceBrand: undefined;

	private queryBuilder: QueryBuilder;

	constructor(
		@ISearchService private readonly searchService: ISearchService,
		@IWorkspaceContextService private readonly workspaceService: IWorkspaceContextService,
		@ICodeseekLogger private readonly logService: ICodeseekLogger,
		@IInstantiationService private readonly instantiationService: IInstantiationService,
		@ICodebaseRemoteService private readonly codebaseRemoteService: ICodebaseService,
		// @ICodebaseSymfService private readonly codebaseSymfService: ICodebaseService,
	) {
		super();
		this.queryBuilder = this.instantiationService.createInstance(QueryBuilder);
	}

	splitByLastColon(input: string): { path: string, range: string } {
		const lastColonIndex = input.lastIndexOf(":");

		if (lastColonIndex === -1) {
			return { path: input, range: '' }; // 没有 ':'，右边为空
		}

		return { path: input.slice(0, lastColonIndex).trim(), range: input.slice(lastColonIndex + 1).trim() };
	}

	parseRange(range: string): { startLineNumber: number; startColumn: number; endLineNumber: number; endColumn: number } | null {
		const numbers = range
			.replace(/[()]/g, '')
			.split('-')
			.flatMap(part => part.split(',').map(num => parseInt(num.trim())));

		if (numbers.length !== 4 || numbers.some(isNaN)) {
			return null;
		}

		return {
			startLineNumber: numbers[0],
			startColumn: numbers[1],
			endLineNumber: numbers[2],
			endColumn: numbers[3]
		};
	}

	concatWithLineNumbers(code: string, startLine: number): string {
		const lines = code.split('\n');
		const result: string[] = [];

		for (let i = startLine, j = 0; j < lines.length; i++, j++) {
			result.push(`${i}|${lines[j]}`);
		}

		return result.join('\n');
	}

	// 语义搜索实现
	async semanticSearch(query: string, topK?: number): Promise<ISearchResult[]> {
		this.logService.info(`[CodebaseService] Semantic search for: ${query}`);

		const workspaceFolders = this.workspaceService.getWorkspace().folders;
		if (!workspaceFolders.length) {
			return [];
		}

		const remoteSearchResults = await this.codebaseRemoteService.search({ userQuery: query, repoUri: workspaceFolders[0].uri, topK })


		let searchResults = [...remoteSearchResults];
		if (topK) {
			searchResults = searchResults.slice(0, topK);
		}
		return searchResults;
	}

	// 文本搜索实现
	async textSearch(query: string, includePattern?: string, excludePattern?: string): Promise<ISearchResult[]> {
		this.logService.info(`[CodebaseService] Text search for: ${query}`);

		const workspaceFolders = this.workspaceService.getWorkspace().folders;
		if (!workspaceFolders.length) {
			return [];
		}

		const searchQuery = this.queryBuilder.text(
			{ pattern: query },  // contentPattern
			workspaceFolders.map(folder => folder.uri),  // folderResources
			{  // options
				includePattern: includePattern ? [includePattern] : undefined,
				excludePattern: excludePattern ? [{ pattern: excludePattern }] : undefined
			}
		);

		const results = await this.searchService.textSearch(searchQuery);

		const searchResults: ISearchResult[] = [];
		results.results.forEach(result => {
			result.results?.forEach(match => {
				if (resultIsMatch(match)) {
					searchResults.push({
						uri: result.resource,
						range: {
							startLineNumber: match.rangeLocations[0].source.startLineNumber,
							startColumn: match.rangeLocations[0].source.startColumn,
							endLineNumber: match.rangeLocations[0].source.endLineNumber,
							endColumn: match.rangeLocations[0].source.endColumn
						},
						content: match.previewText
					});
				}
			});
		});

		return searchResults;
	}

	// 文件搜索实现
	async fileSearch(pattern: string): Promise<URI[]> {
		this.logService.info(`[CodebaseService] File search for: ${pattern}`);

		const workspaceFolders = this.workspaceService.getWorkspace().folders;
		if (!workspaceFolders.length) {
			return [];
		}

		const searchQuery = this.queryBuilder.file(
			workspaceFolders,
			{
				filePattern: pattern
			}
		);

		const results = await this.searchService.fileSearch(searchQuery);

		return results.results.map(result => result.resource);
	}

	override dispose(): void {
		super.dispose();
	}
}

registerSingleton(ICodebaseSearchService, CodebaseSearchService, InstantiationType.Delayed);
