import * as fs from 'fs/promises';
import os from 'node:os'
import path from 'node:path';
import { fileExists, unzip } from '../utils/common.js';
import { ICodeseekLogger } from '../../common/codeseekLogService.js';

type SemverString<Prefix extends string> = `${Prefix}${number}.${number}.${number}`
namespace SemverString {
	const splitPrefixRegex = /^(?<prefix>.*)(?<version>\d+\.\d+\.\d+)$/
	export function forcePrefix<P extends string>(prefix: P, value: string): SemverString<P> {
		const match = splitPrefixRegex.exec(value)
		if (!match || !match.groups?.version) {
			throw new Error(`Invalid semver string: ${value}`)
		}
		return `${prefix}${match.groups?.version}` as SemverString<P>
	}
}


type SymfVersionString = SemverString<'v'>
const symfVersion: SymfVersionString = 'v0.0.16'

enum Platform {
	Linux = 'linux',
	Mac = 'macos',
	Windows = 'windows',
}

enum Arch {
	Arm64 = 'arm64',
	Aarch64 = 'aarch64',
	X86_64 = 'x86_64',
	X64 = 'x64',
	X86 = 'x86',
}

function getOSArch(): {
	platform?: Platform
	arch?: Arch
} {
	const nodePlatformToPlatform: { [key: string]: Platform } = {
		darwin: Platform.Mac,
		linux: Platform.Linux,
		win32: Platform.Windows,
	}
	const nodeMachineToArch: { [key: string]: Arch } = {
		arm64: Arch.Aarch64,
		aarch64: Arch.Aarch64,
		x86_64: Arch.X86_64,
		x64: Arch.X86_64,
		i386: Arch.X86,
		i686: Arch.X86,
	}

	let platform: Platform | undefined
	try {
		platform = nodePlatformToPlatform[os.platform()]
	} catch {
		// Ignore errors
	}

	let arch: Arch | undefined
	try {
		arch = nodeMachineToArch[os.arch()]
	} catch {
		// Ignore errors
	}

	return {
		platform,
		arch,
	}
}

export async function getSymfPath(logger: ICodeseekLogger, dataFolderName: string): Promise<string | null> {

	const { platform, arch } = getOSArch()
	if (!platform || !arch) {
		throw new Error(`Unsupported platform or architecture: ${os.platform()}/${os.arch()}`);
	}

	const { symfPath, symfUnzippedFilename, zigPlatform } = _getSymfPathForPlatform(platform, arch, dataFolderName)

	if (await fileExists(symfPath)) {
		logger.debug('symf', `using downloaded symf "${symfPath}"`);
		return symfPath;
	}
	const symfContainingDir = path.dirname(symfPath)
	const symfFilename = path.basename(symfPath)

	const symfURL = `https://github.com/sourcegraph/symf/releases/download/${symfVersion}/symf-${arch}-${zigPlatform}.zip`
	try {
		const wasDownloaded = await downloadSymfBinary({
			symfPath,
			symfURL,
			symfFilename,
			symfUnzippedFilename,
		}, logger)
		if (wasDownloaded) {
			void removeOldSymfBinaries(symfContainingDir, symfFilename)
		}
		return symfPath

	} catch (error) {
		logger.error('symf', `Failed to download symf binary from ${symfURL}: ${error}`);
		return null;
	}
}

const downloadStatus: {
	[url: string]: {
		inProgress: boolean;
		promise?: Promise<boolean>;
	}
} = {};

async function downloadSymfBinary({
	symfPath,
	symfFilename,
	symfUnzippedFilename,
	symfURL,
}: {
	symfPath: string;
	symfFilename: string;
	symfUnzippedFilename: string;
	symfURL: string;
}, logger: ICodeseekLogger): Promise<boolean> {
	// 检查是否已经有相同URL的下载正在进行
	if (downloadStatus[symfURL]?.inProgress) {
		logger?.info('symf', `Download for ${symfURL} already in progress, waiting for it to complete...`);
		// 如果已经有下载在进行中，返回现有的promise
		return downloadStatus[symfURL].promise || false;
	}

	// 标记此URL的下载正在进行
	downloadStatus[symfURL] = { inProgress: true };

	const tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'symf-'));
	const zipPath = path.join(tempDir, `${symfFilename}.zip`);

	// 创建下载Promise并存储它
	const downloadPromise = (async (): Promise<boolean> => {
		try {
			const axios = (await import('axios')).default;

			logger?.info('symf', `Starting download from ${symfURL}`);

			// 下载文件并显示进度
			const response = await axios({
				method: 'GET',
				url: symfURL,
				responseType: 'arraybuffer',
				timeout: 600000,
				maxRedirects: 5,
				validateStatus: status => status >= 200 && status < 300,
				headers: {
					'User-Agent': 'VSCode-Extension',
				},
				onDownloadProgress: (progressEvent) => {
					const percentCompleted = Math.round(
						(progressEvent.loaded * 100) / (progressEvent.total || 0)
					);
					logger?.info('symf', `Downloading symf: ${percentCompleted}% (${progressEvent.loaded}/${progressEvent.total || 'unknown'} bytes)`);
				}
			});

			// 将下载的内容写入文件
			await fs.writeFile(zipPath, Buffer.from(response.data));

			// 解压文件
			await unzip(zipPath, tempDir);

			const extractedPath = path.join(tempDir, symfUnzippedFilename);

			// 移动二进制文件到目标路径
			await fs.mkdir(path.dirname(symfPath), { recursive: true });
			await fs.rename(extractedPath, symfPath);

			// 使二进制文件可执行
			await fs.chmod(symfPath, 0o755);

			return true;
		} catch (error) {
			logger.error('symf', `Error downloading symf binary: ${error.message}`);
			if (error.response) {
				logger.error('symf', `Response status: ${error.response.status}`);
				logger.error('symf', `Response headers: ${JSON.stringify(error.response.headers)}`);
			}
			if (error.request) {
				logger.error('symf', 'Request was made but no response was received');
			}
			logger.error('symf', `Error details: ${error.stack}`);
			return false;
		} finally {
			// 清理临时目录
			await fs.rm(tempDir, { recursive: true, force: true }).catch(err => {
				logger.error('symf', `Error cleaning up temp directory: ${err.message}`);
			});

			// 标记下载已完成
			downloadStatus[symfURL].inProgress = false;
		}
	})();

	// 存储Promise以便其他调用可以等待它
	downloadStatus[symfURL].promise = downloadPromise;

	return downloadPromise;
}

function _getSymfPathForPlatform(
	platform: Platform,
	arch: Arch,
	dataFolderName: string
): { symfPath: string; symfUnzippedFilename: string; zigPlatform: string } {
	// Releases (eg at https://github.com/sourcegraph/symf/releases) are named with the Zig platform
	// identifier (linux-musl, windows-gnu, macos).
	const zigPlatform =
		platform === Platform.Linux
			? 'linux-musl'
			: platform === Platform.Windows
				? 'windows-gnu'
				: platform


	const symfContainingDir = platform === Platform.Windows
		? path.join(process.execPath, '..', 'tools', 'symf')
		: path.join(os.homedir(), dataFolderName, 'symf', 'bin')

	const symfFilename = `symf-${arch}-${zigPlatform}`
	const symfUnzippedFilename = symfFilename
	const symfPath = path.join(symfContainingDir, symfFilename)
	return { symfPath, symfUnzippedFilename, zigPlatform }
}


async function removeOldSymfBinaries(containingDir: string, currentSymfPath: string): Promise<void> {
	const symfDirContents = await fs.readdir(containingDir)
	const oldSymfBinaries = symfDirContents.filter(f => f.startsWith('symf-') && f !== currentSymfPath)
	for (const oldSymfBinary of oldSymfBinaries) {
		await fs.rm(path.join(containingDir, oldSymfBinary))
	}
}
