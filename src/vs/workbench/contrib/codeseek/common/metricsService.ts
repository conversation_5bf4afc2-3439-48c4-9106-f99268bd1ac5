/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';

/**
 * 定义所有可用的度量事件类型
 */
export const METRICS_EVENT = {
	START: 'start',
	/** 初次安装事件 */
	INSTALL: 'install',
	/** 版本升级事件 */
	UPGRADE: 'upgrade',
	/** 行内聊天事件 */
	CHAT_INLINE: 'chatInline',
	/** 提问事件 */
	CHAT: 'chat',
	/** 代理事件 */
	AGENT: 'agent',
	/** 应用事件 */
	APPLY: 'apply',
	ACCEPT: 'accept',
	REJECT: 'reject',
	ACCEPT_ALL: 'acceptAll',
	REJECT_ALL: 'rejectAll',
} as const;

/**
 * 从 METRICS_EVENT 对象中提取值的类型联合
 */
export type METRICS_EVENT_TYPE = typeof METRICS_EVENT[keyof typeof METRICS_EVENT];

/**
 * 度量服务接口
 */
export interface IMetricsService {
	readonly _serviceBrand: undefined;

	/**
	 * 捕获一个度量事件
	 * @param event 事件类型
	 * @param params 事件相关的参数
	 */
	capture(event: METRICS_EVENT_TYPE, params: Record<string, any>, userId?: string): void;

	/**
	 * 获取调试属性
	 * @returns 包含调试信息的对象
	 */
	getDebuggingProperties(): Promise<object>;
}

export const IMetricsService = createDecorator<IMetricsService>('metricsService');
