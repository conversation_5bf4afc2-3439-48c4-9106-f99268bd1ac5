@echo off
setlocal enabledelayedexpansion

:: 检查并添加 electron 镜像配置到 .npmrc
set "NPMRC_FILE=%USERPROFILE%\.npmrc"

findstr /C:"electron_mirror=https://npmmirror.com/mirrors/electron/" "%NPMRC_FILE%" >nul 2>&1
if errorlevel 1 (
    echo electron_mirror=https://npmmirror.com/mirrors/electron/ >> "%NPMRC_FILE%"
    echo Added electron mirror configuration.
)

findstr /C:"electron_use_remote_checksums=1" "%NPMRC_FILE%" >nul 2>&1
if errorlevel 1 (
    echo electron_use_remote_checksums=1 >> "%NPMRC_FILE%"
    echo Added electron checksum configuration.
)

:: 检查并设置代理环境变量
if not defined ELECTRON_GET_USE_PROXY (
    set "ELECTRON_GET_USE_PROXY=1"
    echo Set ELECTRON_GET_USE_PROXY=1
)

:: 清理不兼容的平台文件（预处理）
echo cleaning incompatible platform files - prebuild stage...
node scripts\clean-prebuilds.js
if errorlevel 1 (
    echo Failed to clean platform files
    exit /b 1
)

:: 运行编译命令
echo Running compilation...
call npm run compile
if errorlevel 1 (
    echo Compilation failed.
    exit /b 1
)

:: 运行打包命令
echo Building VSCode for win32-x64...
call npm run gulp vscode-win32-x64
if errorlevel 1 (
    echo Build failed.
    exit /b 1
)

:: 检查VSCode-win32-x64目录是否存在
cd ..
if not exist "VSCode-win32-x64" (
    echo Error: VSCode-win32-x64 directory not found!
    exit /b 1
)
cd flow-ide

:: 解压symf-x86_64-windows-gnu.zip到tools目录下
echo unzip symf-x86_64-windows-gnu.zip to tools...
if not exist "resources\symf\symf-x86_64-windows-gnu.zip" (
	echo Error: symf-x86_64-windows-gnu.zip not found!
	exit /b 1
)
:: 确保目标目录存在
if not exist "..\VSCode-win32-x64\tools\symf\" (
    mkdir "..\VSCode-win32-x64\tools\symf"
    if errorlevel 1 (
        echo Failed to create symf directory.
        exit /b 1
    )
)
powershell -Command "Expand-Archive -Path resources\symf\symf-x86_64-windows-gnu.zip -DestinationPath ..\VSCode-win32-x64\tools\symf\ -Force"
if errorlevel 1 (
    echo symf-x86_64-windows-gnu.zip unzip failed
    exit /b 1
)

:: 复制 ctags 到 tools 目录下
echo copy ctags_x64.exe/ctags_x86.exe to tools...
:: 检查两个文件是否存在，任意一个不存在则退出
if not exist "resources\ctags\ctags_x64.exe" (
	echo Error: ctags_x64.exe not found!
	exit /b 1
)
if not exist "resources\ctags\ctags_x86.exe" (
	echo Error: ctags_x86.exe not found!
	exit /b 1
)

:: 确保目标目录存在
if not exist "..\VSCode-win32-x64\tools\ctags\" (
    mkdir "..\VSCode-win32-x64\tools\ctags"
    if errorlevel 1 (
        echo Failed to create ctags directory.
        exit /b 1
    )
)
powershell -Command "Copy-Item -Path resources\ctags\ctags_x64.exe -Destination ..\VSCode-win32-x64\tools\ctags\ -Force"
if errorlevel 1 (
    echo Failed to copy ctags_x64.exe.
    exit /b 1
)
powershell -Command "Copy-Item -Path resources\ctags\ctags_x86.exe -Destination ..\VSCode-win32-x64\tools\ctags\ -Force"
if errorlevel 1 (
    echo Failed to copy ctags_x86.exe.
    exit /b 1
)


:: 复制 clangd 到 tools 目录下
echo copy clangd.exe to tools...
:: 检查两个文件是否存在，任意一个不存在则退出
if not exist "resources\clangd\clangd.exe" (
	echo Error: clangd.exe not found!
	exit /b 1
)
:: 确保目标目录存在
if not exist "..\VSCode-win32-x64\tools\clangd\" (
    mkdir "..\VSCode-win32-x64\tools\clangd"
    if errorlevel 1 (
        echo Failed to create clangd directory.
        exit /b 1
    )
)
powershell -Command "Copy-Item -Path resources\clangd\clangd.exe -Destination ..\VSCode-win32-x64\tools\clangd\ -Force"
if errorlevel 1 (
    echo Failed to copy clangd.exe.
    exit /b 1
)

:: 复制 codebase 到 tools 目录下
echo copy codebase.exe to tools...
if not exist "resources\codebase\codebase.exe" (
	echo Error: codebase.exe not found!
	exit /b 1
)
:: 确保目标目录存在
if not exist "..\VSCode-win32-x64\tools\codebase\" (
    mkdir "..\VSCode-win32-x64\tools\codebase"
    if errorlevel 1 (
        echo Failed to create codebase directory.
        exit /b 1
    )
)
powershell -Command "Copy-Item -Path resources\codebase\codebase.exe -Destination ..\VSCode-win32-x64\tools\codebase\ -Force"
if errorlevel 1 (
    echo Failed to copy codebase.exe.
    exit /b 1
)

:: 运行 inno updater
echo Building inno updater...
call npm run gulp vscode-win32-x64-inno-updater
if errorlevel 1 (
    echo Inno updater build failed.
    exit /b 1
)

:: 运行 user setup
echo Building user setup...
call npm run gulp vscode-win32-x64-user-setup
if errorlevel 1 (
    echo User setup build failed.
    exit /b 1
)

:: 检查最终安装包是否生成
if not exist ".build\win32-x64\user-setup\VSCodeSetup.exe" (
    echo Error: VSCodeSetup.exe not found!
    exit /b 1
)

:: 从 package.json 读取版本号
for /f "tokens=2 delims=:," %%I in ('type package.json ^| findstr /C:"\"productVersion\": "') do (
    set "VERSION=%%~I"
)
set "VERSION=%VERSION:"=%"
call set VERSION=%%VERSION: =%%
echo Current version is: %VERSION%

:: 重命名安装包
echo Renaming setup file...
cd .build\win32-x64\user-setup
rename "VSCodeSetup.exe" "FlowUserSetup-x64-%VERSION%.exe"
if errorlevel 1 (
    echo Failed to rename setup file.
    exit /b 1
)
cd ..\..\..

echo Build process completed successfully!
echo Setup file location: .build\win32-x64\user-setup\FlowUserSetup-x64-%VERSION%.exe



