/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as dom from '../../../../base/browser/dom.js';
import { Codicon } from '../../../../base/common/codicons.js';
import { DisposableStore, IDisposable } from '../../../../base/common/lifecycle.js';
import { localize } from '../../../../nls.js';
import { ICommandService } from '../../../../platform/commands/common/commands.js';
import { INotificationService } from '../../../../platform/notification/common/notification.js';
import { registerIcon } from '../../../../platform/theme/common/iconRegistry.js';
import { IThemeService, Themable } from '../../../../platform/theme/common/themeService.js';
import { IChatThreadService } from '../../codeseek/browser/chatThreadService.js';
import { CODESEEK_OPEN_SIDEBAR_ACTION_ID } from '../../codeseek/browser/sidebarActions.js';
import { ISidebarStateService } from '../../codeseek/browser/sidebarStateService.js';
import { TerminalSelection } from '../../codeseek/common/selectedFileService.js';
import './media/terminalContentButton.css';
import { ITerminalGroupService, ITerminalInstance } from './terminal.js';

// 注册显示终端内容按钮的图标
export const showTerminalContentIcon = registerIcon('terminal-show-content', Codicon.clippy, localize('terminalShowContentIcon', 'Icon for showing terminal content in a notification.'));

// 用于创建并管理"显示终端内容"按钮的类
export class TerminalContentButton extends Themable implements IDisposable {
	// 静态实例跟踪器，用于在不同组件之间访问
	private static _currentInstance: TerminalContentButton | undefined;

	private readonly _disposables = new DisposableStore();
	private readonly _instanceDisposables = new Map<number, IDisposable>();
	private _container: HTMLElement | undefined;
	private _button: HTMLElement | undefined;
	private _buttonText: HTMLSpanElement | undefined;
	// 添加跟踪当前终端实例的变量
	private _currentInstance: ITerminalInstance | undefined;

	constructor(
		private readonly _parentElement: HTMLElement,
		@INotificationService private readonly _notificationService: INotificationService,
		@ITerminalGroupService private readonly _terminalGroupService: ITerminalGroupService,
		@IChatThreadService private readonly _chatThreadService: IChatThreadService,
		@ISidebarStateService private readonly _sidebarStateService: ISidebarStateService,
		@ICommandService private readonly _commandService: ICommandService,
		@IThemeService themeService: IThemeService
	) {
		super(themeService);

		// 将当前实例保存到静态变量中
		TerminalContentButton._currentInstance = this;
		this._createButton();

		// 更新按钮上的快捷键显示
		this._updateKeybindingLabel();

		// 初始时隐藏按钮
		this._hideButton();

		// 监听终端激活实例变化
		this._disposables.add(this._terminalGroupService.onDidChangeActiveInstance(instance => {
			this._handleActiveInstanceChanged(instance);
		}));

		// 监听终端选择内容变化
		this._disposables.add(this._terminalGroupService.onDidFocusInstance(instance => {
			this._handleActiveInstanceChanged(instance);
		}));

		// 初始化时检查当前活动终端
		this._handleActiveInstanceChanged(this._terminalGroupService.activeInstance);
	}

	// 处理活动终端实例变化
	private _handleActiveInstanceChanged(instance: ITerminalInstance | undefined): void {
		// 清除旧实例的事件监听
		if (this._currentInstance && this._currentInstance !== instance) {
			const oldDisposable = this._instanceDisposables.get(this._currentInstance.instanceId);
			if (oldDisposable) {
				oldDisposable.dispose();
				this._instanceDisposables.delete(this._currentInstance.instanceId);
			}
		}

		// 设置新的活动实例
		this._currentInstance = instance;

		if (!instance) {
			this._hideButton();
			return;
		}

		// 添加新实例的选择变化监听
		const disposable = instance.onDidChangeSelection(() => {
			this._updateButtonVisibility(instance);
		});
		this._disposables.add(disposable);
		this._instanceDisposables.set(instance.instanceId, disposable);

		// 立即更新按钮可见性
		this._updateButtonVisibility(instance);
	}

	// 更新按钮可见性
	private _updateButtonVisibility(instance: ITerminalInstance): void {
		if (instance.hasSelection()) {
			this._showButton();
			this._updateButtonPosition(instance);
		} else {
			this._hideButton();
		}
	}

	// 新增：更新按钮位置以跟随选择区域
	private _updateButtonPosition(instance: ITerminalInstance): void {
		if (!this._container || !instance.xterm) {
			return;
		}

		try {
			// 获取选择区域的位置信息
			const selectionPosition = instance.xterm.raw.getSelectionPosition();
			if (!selectionPosition) {
				return;
			}

			const font = instance.xterm.getFont();
			if (!font) {
				return;
			}

			// 计算选择区域的像素位置
			// selectionPosition 包含 start 和 end，都有 x, y 坐标（相对于缓冲区）
			const startY = selectionPosition.start.y;
			const endY = selectionPosition.end.y;

			// 计算像素位置
			const charHeight = font.charHeight || 16;

			// 获取当前视口偏移量
			const buffer = instance.xterm.raw.buffer.active;
			const viewportY = buffer.viewportY;

			// 计算相对于视口的位置
			const relativeStartY = startY - viewportY;
			const relativeEndY = endY - viewportY;

			let targetY: number;

			// 根据选择区域位置计算垂直位置
			if (relativeStartY < 0 && relativeEndY < 0) {
				// 选择区域在视口上方，使用顶部位置
				targetY = 10;
			} else if (relativeStartY >= instance.xterm.raw.rows) {
				// 选择区域在视口下方，使用底部位置
				targetY = (instance.xterm.raw.rows - 2) * charHeight;
			} else {
				// 选择区域在视口中，使用选择区域的顶部位置
				targetY = Math.max(0, relativeStartY) * charHeight - 5; // 在选择区域上方5px
			}

			// 设置按钮位置：左右位置固定在最右侧，上下位置跟随选择内容
			this._container.style.position = 'absolute';
			this._container.style.top = `${targetY}px`;
			this._container.style.right = '30px'; // 固定在右侧30px
			this._container.style.left = 'auto'; // 清除left定位

		} catch (error) {
			console.warn('计算按钮位置时出错:', error);
			// 如果计算失败，回退到原来的固定位置
			this._container.style.position = 'absolute';
			this._container.style.top = '30px';
			this._container.style.right = '30px';
			this._container.style.left = 'auto';
		}
	}

	// 显示按钮
	private _showButton(): void {
		if (this._container) {
			this._container.style.display = 'flex';
		}
	}

	// 隐藏按钮
	private _hideButton(): void {
		if (this._container) {
			this._container.style.display = 'none';
		}
	}

	// 静态方法，用于获取当前实例
	public static getCurrentInstance(): TerminalContentButton | undefined {
		return TerminalContentButton._currentInstance;
	}

	// 创建按钮
	private _createButton(): void {
		// 创建一个容器元素
		this._container = document.createElement('div');
		this._container.className = 'terminal-content-button-container';
		// 设置默认位置（在没有选择时的后备位置）
		this._container.style.position = 'absolute';
		this._container.style.top = '30px';
		this._container.style.right = '30px';
		this._parentElement.appendChild(this._container);

		// 创建按钮元素
		this._button = document.createElement('div');
		this._button.className = `terminal-content-button codicon ${showTerminalContentIcon.id}`;

		// 创建文字标签元素，并保存引用以便后续更新
		this._buttonText = document.createElement('span');
		this._buttonText.className = 'terminal-content-button-text';
		this._button.appendChild(this._buttonText);

		this._container.appendChild(this._button);

		// 初始化按钮的文本和提示内容
		this._updateKeybindingLabel();

		// 添加点击事件监听器
		this._disposables.add(dom.addDisposableListener(this._button, dom.EventType.CLICK, () => this._showTerminalContent()));
	}

	// 更新按钮上显示的键绑定标签
	private _updateKeybindingLabel(): void {
		if (!this._button || !this._buttonText) {
			return;
		}
		// 更新按钮提示
		this._button.title = localize('terminal.showContent', "将终端选择添加到聊天");
		this._buttonText.textContent = localize('terminal.showContentLabel', "添加终端选择到聊天");
	}

	// 显示终端内容并将其添加到聊天中
	private async _showTerminalContent(instance?: ITerminalInstance): Promise<void> {
		// 获取活动终端实例
		const activeInstance = instance || this._terminalGroupService.activeInstance;
		if (!activeInstance) {
			this._notificationService.info(localize('terminal.noActiveInstance', "没有活动的终端实例"));
			return;
		}

		try {
			// 验证用户是否在终端中选择了内容
			if (!activeInstance.hasSelection()) {
				return;
			}

			// 获取用户选择的内容
			const content = activeInstance.selection || '';
			if (!content || content.trim() === '') {
				return;
			}

			// 创建一个包含终端内容的对象，作为选择项传递给聊天面板
			const terminalSelection: TerminalSelection = {
				type: 'Terminal',
				content: content,
				title: '终端选择内容',
				fileURI: activeInstance.resource,
				selectionStr: content,
				range: null,
				timestamp: Date.now(),
				fromMention: false
			};

			// 检查聊天面板是否已打开，如果没有则打开它
			if (!this._sidebarStateService.isSidebarChatOpen()) {
				await this._commandService.executeCommand(CODESEEK_OPEN_SIDEBAR_ACTION_ID);
			}

			// 将终端内容添加到聊天中
			this._chatThreadService.addSelectionToChat(this._chatThreadService.getCurrentContainerId(), terminalSelection);
		} catch (error) {
			console.error('处理终端内容时出错', error);
			this._notificationService.error(localize('terminal.processingError', "处理终端内容时出错"));
		}
	}

	// 获取实例方法，用于在外部调用
	public async getContentFromInstance(instance: ITerminalInstance): Promise<void> {
		// 直接调用_showTerminalContent方法，只获取用户选择的内容
		return this._showTerminalContent(instance);
	}

	// 更新按钮在页面中的位置
	public layout(): void {
		// 可以在此处添加布局逻辑
	}

	// 重写主题更新方法以响应主题变化
	override updateStyles(): void {
		super.updateStyles();

		// 强制刷新按钮样式以确保主题变化生效
		if (this._button) {
			// 获取当前主题的颜色值并强制应用
			const computedStyle = getComputedStyle(this._button);
			const bgColor = computedStyle.getPropertyValue('--vscode-button-secondaryBackground');
			const textColor = computedStyle.getPropertyValue('--vscode-button-secondaryForeground');

			// 如果CSS变量存在，强制重新应用
			if (bgColor) {
				this._button.style.setProperty('background-color', `var(--vscode-button-secondaryBackground)`, 'important');
			}
			if (textColor) {
				this._button.style.setProperty('color', `var(--vscode-button-secondaryForeground)`, 'important');
			}

			// 触发重绘
			this._button.style.display = 'none';
			this._button.offsetHeight; // 强制重排
			this._button.style.display = '';
		}
	}

	// 资源释放
	override dispose(): void {
		// 如果当前静态实例引用指向此实例，则清除引用
		if (TerminalContentButton._currentInstance === this) {
			TerminalContentButton._currentInstance = undefined;
		}

		// 清理实例特定的可释放对象
		for (const disposable of this._instanceDisposables.values()) {
			disposable.dispose();
		}
		this._instanceDisposables.clear();

		this._disposables.dispose();
		this._container?.remove();
		this._container = undefined;
		this._button = undefined;
		this._buttonText = undefined;

		super.dispose();
	}
}
