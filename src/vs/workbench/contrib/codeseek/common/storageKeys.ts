/*--------------------------------------------------------------------------------------
 *  Copyright 2025 Glass Devtools, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

// past values:

// 1.0.0
export const CODESEEK_SETTINGS_STORAGE_KEY = 'flow.settingsServiceStorage';


// past values:


// 0.1.18
// export const THREAD_STORAGE_KEY = 'flow.chatThreadStorage';
// 0.1.19
export const THREAD_ABSTRACT_STORAGE_KEY = 'flow.chatThreadStorageAbstract'
export const THREAD_MESSAGES_STORAGE_KEY = 'flow.chatThreadStorageMessages'

export const ZTE_USER_INFO_KEY = 'flow.zteUserInfo'
