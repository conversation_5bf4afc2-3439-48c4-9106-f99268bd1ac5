/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { Disposable } from '../../../../../base/common/lifecycle.js';
import { registerSingleton, InstantiationType } from '../../../../../platform/instantiation/common/extensions.js';


import { IConfigurationService } from '../../../../../platform/configuration/common/configuration.js';
import { IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';
import { INotificationService } from '../../../../../platform/notification/common/notification.js';
import { IMainProcessService } from '../../../../../platform/ipc/common/mainProcessService.js';
import { IChannel } from '../../../../../base/parts/ipc/common/ipc.js';
import { ZTE_USER_LOGIN_CHANNEL_NAME, ZteUserLoginChannelCommand } from './zteUserTypes.js';
import { Emitter, Event } from '../../../../../base/common/event.js';
import { CancellationToken } from '../../../../../base/common/cancellation.js';

import { UserInfo, ICodeseekUacLoginService, AuthInfo } from './UacloginTypes.js';
import { IAuthenticationService } from '../../../../services/authentication/common/authentication.js';
import { ZteAuthenticationProvider } from './ZteAuthenticationProvider.js';
import { CODESEEK_OPEN_UAC_LOGIN_ACTION_ID } from '../../browser/codeseekSettingsUacLogin.js';
import { ICommandService } from '../../../../../platform/commands/common/commands.js';





class CodeseekUacLoginService extends Disposable implements ICodeseekUacLoginService {
	_serviceBrand: undefined;

	private readonly http_proxy_key = 'http.proxy';
	private readonly https_proxy_key = 'https.proxy';
	private readonly _onDidChangeUserInfo = new Emitter<UserInfo>();
	private readonly _onDidUserLogin = new Emitter<UserInfo>();
	private readonly _onDidUserLogout = new Emitter<UserInfo>;

	onDidChangeUserInfo: Event<UserInfo> = this._onDidChangeUserInfo.event;
	onDidUserLogin: Event<UserInfo> = this._onDidUserLogin.event;
	onDidUserLogout: Event<UserInfo> = this._onDidUserLogout.event;
	private channel: IChannel;


	constructor(
		@IConfigurationService private readonly _configurationService: IConfigurationService,
		@INotificationService private readonly notificationService: INotificationService,
		@IMainProcessService readonly mainProcessService: IMainProcessService,
		@IAuthenticationService readonly authenticationService: IAuthenticationService,
		@IInstantiationService readonly instantiationService: IInstantiationService,
		@ICommandService private readonly commandService: ICommandService,
	) {
		super();
		this.channel = mainProcessService.getChannel(ZTE_USER_LOGIN_CHANNEL_NAME);

		const kaTimer = setInterval(() => {
			const zteAuthenticationProvider = instantiationService.createInstance(ZteAuthenticationProvider);
			authenticationService.registerAuthenticationProvider("ZTE", zteAuthenticationProvider)
			clearInterval(kaTimer)
		}, 2000);
		this.channel.listen<UserInfo>(ZteUserLoginChannelCommand.ON_DID_CHANGE_USERINFO)(userInfo => {
			this._onDidChangeUserInfo.fire(userInfo);
		});
		this.channel.listen<UserInfo>(ZteUserLoginChannelCommand.ON_DID_USER_LOGIN)(userInfo => {
			this._onDidUserLogin.fire(userInfo);
			this.notificationService.info(`${userInfo.username}已成功登录！`);
		});

		this.channel.listen<UserInfo>(ZteUserLoginChannelCommand.ON_DID_USER_LOGOUT)(userInfo => {
			this._onDidUserLogout.fire(userInfo);
			this.notificationService.info(`${userInfo.username}已登出！`);
		});

		this.channel.listen(ZteUserLoginChannelCommand.ON_OPEN_UAC_LOGIN_PAGE)(e => {
			this.commandService.executeCommand(CODESEEK_OPEN_UAC_LOGIN_ACTION_ID);
		});
	}

	public async udsLogin(): Promise<void> {
		const result: any = await this.channel.call(ZteUserLoginChannelCommand.UDS_LOGIN)
		if (result.error) {
			this.notificationService.info(`${result.error}`);
		}
	}

	public async codeServerLogin(cookies: string): Promise<void> {
		const result: any = await this.channel.call(ZteUserLoginChannelCommand.CODE_SERVER_LOGIN, [cookies])

		if (result.error) {
			this.notificationService.error(`${result.error}`);
		}
	}

	public async qrLogin(dataStr: string, cancellationToken: CancellationToken): Promise<void> {
		try {
			const result: any = await this.channel.call(ZteUserLoginChannelCommand.QR_LOGIN, [dataStr], cancellationToken)
			if (result.error) {
				this.notificationService.info(`${result.error}`);
			}
		} catch (e) {
			if (e.name !== 'Canceled') {
				this.notificationService.info(`${e}`)
			}
		}
	}

	public async login(authInfo: AuthInfo): Promise<boolean> {
		const result: any = await this.channel.call(ZteUserLoginChannelCommand.LOGIN_WITH_PWD, [authInfo])

		if (result.error) {
			this.notificationService.error(`${result.error}`);
			return false;
		} else {
			return true
		}
	}

	public async logout(): Promise<void> {
		await this.channel.call(ZteUserLoginChannelCommand.LOGOUT);
	}

	public async getUserInfo(): Promise<UserInfo | undefined> {
		return await this.channel.call(ZteUserLoginChannelCommand.GET_USER_INFO);
	}
	public async check() {
		const httpProxy = this._configurationService.getValue<string>(this.http_proxy_key);
		const httpsProxy = this._configurationService.getValue<string>(this.https_proxy_key)
		const results: string[] = await this.channel.call(ZteUserLoginChannelCommand.PROMBLES_CHECK, [httpProxy, httpsProxy])
		return results;
	}
}


registerSingleton(ICodeseekUacLoginService, CodeseekUacLoginService, InstantiationType.Eager);
