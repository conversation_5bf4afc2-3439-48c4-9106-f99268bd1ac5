import { MD5 } from "./crypt.js";
import { getGerritHttpPassword } from "./gerrit.js";
import { IRequestService } from '../../../../../../platform/request/common/request.js';
import { request as request_ } from './request.js';
import request from 'request'
import { CancellationToken } from '../../../../../../base/common/cancellation.js';

export function queryUserInfo(requestService: IRequestService, userId: string, token: string, udsVersion?: string) {
	return new Promise(function (resolve, reject) {
		request_(
			requestService,
			{
				url: 'https://icenterapi.zte.com.cn/zte-km-icenter-address/rest/user/queryUserCard?employeeShortId=' + userId + '&curEmployeeShortId=' + userId,
				type: "GET",
				json: true,
				headers: {
					"X-Auth-Value": token,
					"X-Emp-No": userId,
					"Content-Type": "application/json;charset=utf-8"
				}
			}, async function (error: Error | null, response: string | null, body: any) {
				if (error) { return reject(error); }
				if (body && body.code && body.code.code === '0000') {
					const userInfo = body.bo[0];
					const trackInfo = { id: userId, workplace: userInfo.longWorkPlace, department: userInfo.deptName, udsVersion };
					getGerritHttpPassword(requestService, userId, token).then((gerritHttpPassword) => {
						resolve({ userId: userId, username: userInfo.name + userId, department: userInfo.deptFullName, token: token, gerritHttpPassword: gerritHttpPassword, trackInfo });
					}).catch(err => {
						resolve({ userId: userId, username: userInfo.name + userId, department: userInfo.deptFullName, token: token, gerritHttpPassword: null, trackInfo });
					});
				} else {
					reject(body?.bo?.msg);
				}
			});
	});
};

function run(requestService: IRequestService, dataStr: string): Promise<any> {
	const options = {
		url: "https://uac.zte.com.cn/uacqr/auth/qrcode/verify.serv",
		proxy: null,
		type: "POST",
		json: true,
		body: dataStr,
		headers: {
			"Content-Type": "application/json",
			Accept: "application/json;api-version=3.0-preview.1",
			"Accept-Encoding": "gzip",
			"Content-Length": String(dataStr.length),
		},
	};

	return new Promise(function (resolve, reject) {
		request_(requestService, options, async function (error: any, response: any, body: any) {
			if (error) {
				console.error(JSON.stringify(error));
			};
			console.log("轮询中...");
			if (body && body.code && body.code.code === "0000" && body.bo?.code === "0000") {
				const userId = body.other.account;
				const token = body.other.token;
				const userInfo = await queryUserInfo(requestService, userId, token);
				console.log("扫码成功，获取人事信息...");
				resolve(userInfo);
			} else if (body && body.code && body.code.code === "0000" && body.bo?.code !== "4002") {
				resolve({ error: "二维码已失效！" });
			} else {
				reject(body?.bo?.code);
			}
		}).catch(e => {
			console.error(e);
		});
	});
}

export async function pollUac(requestService: IRequestService, dataStr: string, cancellationToken?: CancellationToken) {
	try {
		const result = await new Promise((resolve, reject) => {
			let attempts = 0;
			const intervalId = setInterval(async () => {
				if (cancellationToken && cancellationToken.isCancellationRequested) {
					clearInterval(intervalId);
					resolve(undefined);
				}
				try {
					const response = await run(requestService, dataStr);
					if (response) {
						clearInterval(intervalId);
						resolve(response);
					}
				} catch (error) {
					attempts++;
					if (attempts >= 300) {
						clearInterval(intervalId);
						reject(new Error('UAC扫码登陆定时器已超时！'));
					}
				}
			}, 500);
		});
		return result;
	} catch (error) {
		return { error: `定时器已超时！${error}` };
	}
}

async function getUdsToken(port: number) {
	return new Promise((resolve, reject) => {
		request({
			url: 'http://127.0.0.1:' + port + '/requestToken',
			proxy: null,
			method: "GET",
			json: true,
			headers: {
				Host: "127.0.0.1:" + port,
				Referer: "https://uac.zte.com.cn/"
			}
		}, async function (error: any, response: any, body: any) {
			if (!error && body && typeof (body) === "string" && body.length > 2) {
				try {
					const result = JSON.parse(body.substring(1, body.length - 1));
					resolve(result);
				} catch (error) {
					reject(error);
				}
			} else {
				reject(error || response.statusCode);
			}
		});
	});
}

async function tryGetUdsToken(ports: number[], retries = 3) {
	for (let i = 0; i < ports.length * retries; i++) {
		const port = ports[i % ports.length];
		try {
			const data = await getUdsToken(port);
			return data;
		} catch (err) {
			console.error(err);
		}
	}
	throw new Error('All requests failed');
}

export function udsPwdFree(requestService: IRequestService) {
	return new Promise(async function (resolve) {
		try {
			const result: any = await tryGetUdsToken([19995, 19996, 5864, 5865, 29272, 29273]);
			console.log(result);
			const userId = result?.ZTEDPGSSOUser || result?.PORTALSSOUser;
			const token = result?.ZTEDPGSSOCookie || result?.PORTALSSOCookie;
			const userInfo = await queryUserInfo(requestService, userId, token, result?.udsVersion);
			resolve(userInfo);
		} catch (error) {
			resolve({ error: 'uds未登录或状态异常，请改用iCenter扫码登录！' });
		}
	});
}

export function loginWithPwd(requestService: IRequestService, userId: string, password: string, dynPwd: string) {
	return new Promise(function (resolve, reject) {
		const ip = '127.0.0.1';
		const originSystemCode = "";
		const loginSystemCode = "************";
		const postData = {
			account: userId,
			passWord: password,
			dypwd: dynPwd,
			loginClientIp: ip,
			originSystemCode: originSystemCode,
			loginSystemCode: loginSystemCode,
			verifyCode: MD5.instance.hex_md5(userId + password + dynPwd + ip + loginSystemCode + originSystemCode)
		};

		request_(requestService, {
			url: 'https://uac.zte.com.cn/uacdynauth/auth/dynpwd/verify.serv',
			type: "POST",
			json: true,
			headers: {
				"content-type": "application/json",
			},
			body: JSON.stringify(postData)
		}, async function (error: any, response: any, body: any) {
			if (!error) {
				if (body && body.code && body.code.code === "0000" && body.bo?.code === "0000") {
					const token = body.other.token;
					const userInfo = await queryUserInfo(requestService, userId, token);
					resolve(userInfo);
				} else {
					resolve({ error: body?.bo ? body.bo.msg : body.code?.msg });
				}
			} else {
				resolve({ error: "登录失败，请确认账号信息正确！" });
			}
		});
	});
}

function parseCookie(name: string, cookies: string) {
	const cookieArray = cookies.split(';');
	for (let i = 0; i < cookieArray.length; i++) {
		const cookie = cookieArray[i].trim();
		if (cookie.startsWith(`${name}=`)) {
			return cookie.substring((`${name}=`).length, cookie.length);
		}
	}
	return null;
}

export function codeServerPwdFree(requestService: IRequestService, cookies: string) {
	const userId = parseCookie('PORTALSSOUser', cookies) || parseCookie('ZTEDPGSSOUser', cookies);
	const token = parseCookie('PORTALSSOCookie', cookies) || parseCookie('ZTEDPGSSOCookie', cookies);

	if (!userId || !token) { return Promise.resolve({ error: "自动登录失败，cookie信息异常，请扫码或者刷新浏览器页面" }); }

	return new Promise(async function (resolve, reject) {
		queryUserInfo(requestService, userId, token).then(userInfo => resolve(userInfo)).catch(err => resolve({ error: "自动登录失败，cookie信息失效，请扫码或者刷新浏览器页面" }));
	});
}
