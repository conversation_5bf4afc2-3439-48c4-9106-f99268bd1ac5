import { Event } from '../../../../../base/common/event.js';
import { IServerChannel } from '../../../../../base/parts/ipc/common/ipc.js';
import { IRestfulApiMainService } from '../../common/restfulApiMainService.js';

export class RestfulApiIpcChannel implements IServerChannel {

	constructor(
		private readonly restfulApiMainService: IRestfulApiMainService
	) {
	}

	listen<T>(_: unknown, event: string, arg?: any): Event<T> {
		switch (event) {
			default:
				throw new Error(`Unknown event: ${event}`);
		}
	}

	/**
	 * 处理方法调用
	 */
	call(_: unknown, command: string, args: any[] = []): Promise<any> {
		switch (command) {
			case 'getAllModels':
				return this.restfulApiMainService.getAllModels();
			case 'initialize':
				return this.restfulApiMainService.initialize();
			default:
				throw new Error(`Unknown command: ${command}`);
		}
	}
}
