/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

// register inline diffs
import './editCodeService.js';

// register Sidebar pane, state, actions (keybinds, menus) (Ctrl+L)
import './sidebarActions.js';
import './sidebarPane.js';
import './sidebarStateService.js';

// register ctags actions
import './ctagsActions.js';

// register quick edit (Ctrl+K)
import './quickEditActions.js';

// register selection tip
import './tipActions.js';

// register diff actions
import './diffActions.js';

// register Thread History
import './chatThreadService.js';

// register Autocomplete
import './autocompleteService.js';

// register Context services
// import './contextGatheringService.js'
// import './contextUserChangesService.js'
import './codeseekCodeSelectionService.js';
import './codebaseSearchService.js';

// settings pane
import './codeseekSettingsPane.js';

// register css
import './media/codeseek.css';

// update (frontend part, also see platform/)
import './codeseekUpdateActions.js';

// register ClangdSymbolServiceFileOpenHandler
import { Registry } from '../../../../platform/registry/common/platform.js';
import { Extensions as WorkbenchExtensions, IWorkbenchContributionsRegistry } from '../../../common/contributions.js';
import { LifecyclePhase } from '../../../services/lifecycle/common/lifecycle.js';
import { ClangdSymbolServiceFileOpenHandler } from './clangdSymbolServiceFileOpenHandler.js';

// Register the ClangdSymbolServiceFileOpenHandler as a workbench contribution
Registry.as<IWorkbenchContributionsRegistry>(WorkbenchExtensions.Workbench)
	.registerWorkbenchContribution(ClangdSymbolServiceFileOpenHandler, LifecyclePhase.Restored);


// ---------- common (unclear if these actually need to be imported, because they're already imported wherever they're used) ----------

// llmMessage
import '../common/llmMessageService.js';

// codeseekSettings
import '../common/codeseekSettingsService.js';

// refreshModel
import '../common/refreshModelService.js';

// metrics
import './metricsService.js';

// updates
import '../common/codeseekUpdateService.js';

// tools
import '../common/toolsService.js';

// uacLogin
import '../common/uac/codeseekUacLoginService.js'
