import { Widget } from '../../../../../base/browser/ui/widget.js';
import { ICodeEditor, IOverlayWidget } from '../../../../../editor/browser/editorBrowser.js';
import * as dom from '../../../../../base/browser/dom.js';
import { EditorOption } from '../../../../../editor/common/config/editorOptions.js';
import { EDITOR_LEFT_MARGIN_WIDTH } from './index.js';
import { ISidebarStateService } from '../sidebarStateService.js';
import { IQuickEditStateService } from '../quickEditStateService.js';
import { IEditCodeService } from '../editCodeService.js';

type TextSelectionTipWidgetProps = {
	editor: ICodeEditor;
	startLine: number;
	endLine: number;
	isOpenChat: boolean;
	isOpenEdit: boolean;
	onOpenChat: () => void;
	onOpenEdit: () => void;
	onExplainCode?: () => void;
};

export class TextSelectionTipWidget extends Widget implements IOverlayWidget {
	private readonly ID: string;
	private readonly type: string;
	private readonly _domNode: HTMLElement;
	private readonly initialLeftPx: number;
	constructor(
		private readonly props: TextSelectionTipWidgetProps,
		private readonly _editCodeService: IEditCodeService,
		private readonly _sidebarStateService: ISidebarStateService,
		private readonly _quickEditStateService: IQuickEditStateService,
	) {
		super();

		this.ID = `textselectiontip-${Date.now()}`;
		this.type = 'TextSelectionTipZone';
		this.initialLeftPx = this.props.editor.getScrolledVisiblePosition({ lineNumber: 1, column: 1 })?.left ?? EDITOR_LEFT_MARGIN_WIDTH;

		// Create container div with buttons
		const { commandButton, commandButtonText,
			chatButton, chatButtonText, chatShortcut,
			editButton, editButtonText, editShortcut, buttons
		} = dom.h('div@buttons', [
			dom.h('div@chatButton', [
				dom.h('span@chatButtonText', []),
				dom.h('span@chatShortcut', [])
			]),
			dom.h('div@editButton', [
				dom.h('span@editButtonText', []),
				dom.h('span@editShortcut', [])
			]),
			dom.h('div@commandButton', [
				dom.h('span@commandButtonText', []),
			])
		]);

		// Style the container
		buttons.style.display = 'flex';
		buttons.style.position = 'absolute';
		buttons.style.backgroundColor = 'var(--vscode-editor-background)';
		buttons.style.border = '1px solid var(--vscode-commandCenter-activeBorder)';
		buttons.style.borderRadius = '3px';
		buttons.style.padding = '0';
		buttons.style.height = `${props.editor.getOption(EditorOption.lineHeight) + 1}px`;
		buttons.style.zIndex = '1';
		buttons.id = 'buttons';

		// 为容器添加点击事件处理，阻止事件冒泡
		buttons.addEventListener('click', (e) => {
			e.stopPropagation();
		});

		// 为容器添加mousedown事件处理，阻止事件冒泡和默认行为
		buttons.addEventListener('mousedown', (e) => {
			e.stopPropagation();
			e.preventDefault();
		});

		// Common button styles
		const commonButtonStyle = {
			height: '100%',
			border: 'none',
			backgroundColor: 'transparent',
			color: 'var(--vscode-editor-foreground)',
			fontSize: '15px',
			cursor: 'pointer',
			display: 'flex',
			alignItems: 'center',
			gap: '3px',
			userSelect: 'none',
		};

		const commandButtonStyle = {
			...commonButtonStyle,
			padding: '0px 10px 0px 8px',
		};

		const chatButtonStyle = {
			...commonButtonStyle,
			padding: '0px 10px 0px 8px',
		};

		const editButtonStyle = {
			...commonButtonStyle,
			padding: '0px 8px 0px 10px',
		};


		// Common shortcut styles
		const shortcutStyle = {
			color: 'var(--vscode-editorGhostText-foreground)',
			fontSize: '13px',
			display: 'flex',
			alignItems: 'center',
			height: '100%',
		};

		// Command dropdown
		const commandDropdown = document.createElement('div');
		const commadnDropStyle = {
			display: 'none',
			position: 'absolute',
			backgroundColor: 'var(--vscode-editor-background)',
			border: '1px solid var(--vscode-commandCenter-activeBorder)',
			borderRadius: '3px',
			zIndex: '2',
			boxShadow: '0 2px 8px var(--vscode-widget-shadow)',
			left: '100%',
			whiteSpace: 'nowrap'
		}
		commandDropdown.id = 'commandDropdown';
		Object.assign(commandDropdown.style, commadnDropStyle);

		const explainCodeContainer = document.createElement('div');
		const explainCodeItemStyle = {
			cursor: 'cursor',
			borderRadius: '3px',
			display: 'flex',
			alignItems: 'center',
			padding: '2px 5px',
			gap: '5px',
			fontSize: '15px',
			height: `${props.editor.getOption(EditorOption.lineHeight)}px`,
			justifyContent: 'space-between',
			width: '100%',
			boxSizing: 'border-box'
		}

		const explainTextSpan = document.createElement('span');
		explainTextSpan.textContent = 'Explain Code';

		const explainShortcutSpan = document.createElement('span');
		explainShortcutSpan.textContent = 'Ctrl+Alt+E';
		Object.assign(explainShortcutSpan.style, shortcutStyle);

		explainCodeContainer.appendChild(explainTextSpan);
		explainCodeContainer.appendChild(explainShortcutSpan);

		Object.assign(explainCodeContainer.style, explainCodeItemStyle);

		explainCodeContainer.addEventListener('mouseover', () => {
			explainCodeContainer.style.backgroundColor = 'var(--vscode-list-hoverBackground)';
		});

		explainCodeContainer.addEventListener('mouseout', () => {
			explainCodeContainer.style.backgroundColor = 'transparent';
		});

		explainCodeContainer.addEventListener('mousedown', (e) => {
			e.stopPropagation();
			e.preventDefault();
			if (props.onExplainCode) {
				props.onExplainCode();
			}
			commandDropdown.style.display = 'none';
		});

		commandDropdown.appendChild(explainCodeContainer);
		buttons.appendChild(commandDropdown);

		// Style command button
		commandButtonText.textContent = 'Command';
		commandButtonText.id = 'commandButtonText';
		commandButton.id = 'commandButton';
		Object.assign(commandButton.style, commandButtonStyle);

		// Show dropdown on command button hover
		commandButton.addEventListener('mouseover', () => {
			commandDropdown.style.display = 'block';
		});

		buttons.addEventListener('mouseleave', () => {
			commandDropdown.style.display = 'none';
		});
		chatButton.addEventListener('mouseover', () => {
			commandDropdown.style.display = 'none';
		});
		editButton.addEventListener('mouseover', () => {
			commandDropdown.style.display = 'none';
		});

		// Style chat button
		const handleChatClick = (e: MouseEvent) => {
			e.stopPropagation(); // 阻止事件冒泡
			e.preventDefault(); // 阻止默认行为
			props.onOpenChat();
		};

		// 使用mousedown事件代替click事件
		chatButton.addEventListener('mousedown', handleChatClick);
		chatButtonText.addEventListener('mousedown', handleChatClick);
		chatShortcut.addEventListener('mousedown', handleChatClick);

		// 保留click事件处理，以防mousedown不触发
		chatButton.addEventListener('click', handleChatClick);
		chatButtonText.addEventListener('click', handleChatClick);
		chatShortcut.addEventListener('click', handleChatClick);

		chatButtonText.textContent = props.isOpenChat ? 'Add to Chat' : 'Chat';
		chatShortcut.textContent = 'Ctrl+L';
		chatButtonText.id = 'chatButtonText';
		chatShortcut.id = 'chatShortcut';
		chatButton.id = 'chatButton';
		Object.assign(chatButton.style, chatButtonStyle);
		Object.assign(chatShortcut.style, shortcutStyle);

		// Style edit button
		const handleEditClick = (e: MouseEvent) => {
			e.stopPropagation(); // 阻止事件冒泡
			e.preventDefault(); // 阻止默认行为
			props.onOpenEdit();
		};

		// 使用mousedown事件代替click事件
		editButton.addEventListener('mousedown', handleEditClick);
		editButtonText.addEventListener('mousedown', handleEditClick);
		editShortcut.addEventListener('mousedown', handleEditClick);

		// 保留click事件处理，以防mousedown不触发
		editButton.addEventListener('click', handleEditClick);
		editButtonText.addEventListener('click', handleEditClick);
		editShortcut.addEventListener('click', handleEditClick);

		editButtonText.textContent = 'Edit';
		editShortcut.textContent = 'Ctrl+K';
		editButtonText.id = 'editButtonText';
		editShortcut.id = 'editShortcut';
		editButton.id = 'editButton';
		Object.assign(editButton.style, editButtonStyle);
		Object.assign(editShortcut.style, shortcutStyle);

		// Hover effects
		const addHoverEffect = (button: HTMLElement) => {
			button.addEventListener('mouseover', () => {
				button.style.backgroundColor = 'var(--vscode-codeseek-selectionFileBG)';
			});
			button.addEventListener('mouseout', () => {
				button.style.backgroundColor = 'transparent';
			});
		};

		addHoverEffect(commandButton);
		addHoverEffect(chatButton);
		addHoverEffect(editButton);

		this._domNode = buttons;

		// 计算并更新位置
		const updatePosition = () => {
			const model = this.props.editor.getModel();
			if (!model) return;
			const selection = this.props.editor.getSelection();
			if (!selection) {
				this._editCodeService.disposeTip('TextSelectionTipZone');
				return;
			}

			// 检查当前行及其上方3行
			let displayLine = this.props.startLine;
			let displayColumn = model.getLineContent(this.props.startLine)?.length + 1;


			for (let i = 1; i <= 2; i++) {
				const lineNumber = this.props.startLine - i;
				if (lineNumber <= 0) break;

				const lineContent = model.getLineContent(lineNumber);
				if (!lineContent) {
					displayLine = lineNumber;
					break;
				}

				const lineColumn = model.getLineContent(lineNumber)?.length + 1;
				if (lineColumn < displayColumn) {
					displayLine = lineNumber;
					displayColumn = lineColumn;
					continue;
				}
			}

			if (displayLine === this.props.startLine) {
				for (let i = 1; i <= 2; i++) {
					const lineNumber = this.props.endLine + i;
					if (lineNumber > model.getLineCount()) break;

					const lineContent = model.getLineContent(lineNumber);
					if (!lineContent) {
						displayLine = lineNumber;
						break;
					}

					const lineColumn = model.getLineContent(lineNumber)?.length + 1;
					if (lineColumn < displayColumn) {
						displayLine = lineNumber;
						displayColumn = lineColumn;
						continue;
					}
				}
			}

			const topPx = this.props.editor.getTopForLineNumber(displayLine) - this.props.editor.getScrollTop();
			this._domNode.style.top = `${topPx}px`;

			const layoutInfo = this.props.editor.getLayoutInfo();
			const buttonWidth = this._domNode.offsetWidth;
			const minimapWidth = layoutInfo.minimap.minimapWidth;
			const verticalScrollbarWidth = layoutInfo.verticalScrollbarWidth;

			const maxLeftPx = layoutInfo.width - buttonWidth - minimapWidth - verticalScrollbarWidth - 10;
			if (model.getLineCount() === 1) {
				const selection = this.props.editor.getSelection();
				if (!selection) return;

				const selectionStartColumn = selection.startColumn;
				const selectionEndColumn = selection.endColumn;
				if (!selectionEndColumn || !selectionStartColumn) return;
				const position = this.props.editor.getPosition();
				if (!position) return;

				const isLeftToRight = position.lineNumber === selection.endLineNumber &&
					position.column === selection.endColumn;

				let leftPx = 0;

				if (isLeftToRight) {
					const selectionEndPosition = {
						lineNumber: selection.endLineNumber,
						column: selection.endColumn
					};

					const selectionEndCoords = this.props.editor.getScrolledVisiblePosition(selectionEndPosition);
					if (!selectionEndCoords) return;

					const targetLeftPx = selectionEndCoords.left + 50;

					if (targetLeftPx <= maxLeftPx) {
						leftPx = targetLeftPx;
					} else {
						leftPx = this.initialLeftPx + 20;
					}
				} else {
					const selectionStartPosition = {
						lineNumber: selection.startLineNumber,
						column: selection.startColumn
					};

					const selectionStartCoords = this.props.editor.getScrolledVisiblePosition(selectionStartPosition);
					if (!selectionStartCoords) return;

					const targetLeftPx = selectionStartCoords.left - this._domNode.offsetWidth - 50;

					if (targetLeftPx >= this.initialLeftPx) {
						leftPx = targetLeftPx;
					} else {
						leftPx = maxLeftPx;
					}
				}

				this._domNode.style.left = `${leftPx}px`;
			} else {
				const displayPosition = {
					lineNumber: displayLine,
					column: displayColumn
				};

				const displayPositionCoords = this.props.editor.getScrolledVisiblePosition(displayPosition);
				if (!displayPositionCoords) return;
				const contentBasedLeftPx = displayPositionCoords.left + 20;
				this._domNode.style.left = `${Math.min(contentBasedLeftPx, maxLeftPx)}px`;
			}
		};

		const updateText = () => {
			const selection = this.props.editor.getSelection();
			if (!selection) {
				this._editCodeService.disposeTip('TextSelectionTipZone');
				return;
			}
			chatButtonText.textContent = this._sidebarStateService.isSidebarChatOpen() ? 'Add to Chat' : 'Chat';
			// editButtonText.textContent = this._quickEditStateService.isOpenEdit() ? 'Add to Edit' : 'Edit';
		};

		this.props.editor.addOverlayWidget(this);
		updatePosition();
		updateText();
		this._register(this._sidebarStateService.onDidChangeChatViewVisibility(() => updateText()));
		this._register(this._quickEditStateService.onDidChangeQuickEditVisibility(() => updateText()));

		this._register(this.props.editor.onDidScrollChange(() => {
			updatePosition();
			commandDropdown.style.display = 'none';
		}));
		this._register(this.props.editor.onDidLayoutChange(() => {
			updatePosition();
			commandDropdown.style.display = 'none';
		}));
	}

	public getId(): string {
		return this.ID;
	}

	public getType(): string {
		return this.type;
	}

	public getDomNode(): HTMLElement {
		return this._domNode;
	}

	public getPosition() {
		return null; // 返回 null 因为我们手动控制位置
	}

	public override dispose(): void {
		this.props.editor.removeOverlayWidget(this);
		super.dispose();
	}
}
