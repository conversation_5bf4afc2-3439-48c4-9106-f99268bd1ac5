import { Disposable } from '../../../../base/common/lifecycle.js';
import { URI } from '../../../../base/common/uri.js';
import { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { LogLevel } from '../../../../platform/log/common/log.js';
import { ILoggerMainService } from '../../../../platform/log/electron-main/loggerService.js';
import { IProductService } from '../../../../platform/product/common/productService.js';
import { ClangdRunner } from '../electron-main/clangd/clangdRunner.js';
import { SymbolReference, SymbolReferencesQuery } from '../electron-main/codebase/symbolReferences.js';

/**
 * 服务接口定义，提供基于clangd的符号定义查询功能
 */
export interface IClangdMainService {
	readonly _serviceBrand: undefined;

	initialize(scopeDir: URI, clangdCompileCommandsDir?: string): Promise<void>;
	/**
	 * 获取符号定义结果
	 * @param symbolName 要查询的符号名称
	 * @param scopeDirs 要查询的目录范围
	 * @param filter 可选的过滤条件
	 */
	getSymbolReferences(scopeDir: URI, symbolReferencesQuery: SymbolReferencesQuery): Promise<SymbolReference[]>;
	/**
	 * 通知语言服务器打开文档
	 * @param documentUri 文档URI
	 */
	didOpen(scopeDir: URI, documentUri: string): Promise<void>;
}

export const IClangdMainService = createDecorator<IClangdMainService>('clangdMainService');

/**
 * 主进程中的 clangd 服务实现
 */
export class ClangdMainService extends Disposable implements IClangdMainService {
	readonly _serviceBrand: undefined;
	private _clangdRunner: ClangdRunner | undefined;

	constructor(
		@ILoggerMainService private readonly mainLogger: ILoggerMainService,
		@IProductService private readonly productService: IProductService
	) {
		super();
	}

	/**
	 * 初始化
	 */
	async initialize(scopeDir: URI, clangdCompileCommandsDir?: string): Promise<void> {
		const logger = this.mainLogger.createLogger('clangd-main-service');
		logger.setLevel(LogLevel.Debug);
		const dataFolderName = this.productService.dataFolderName;
		this._clangdRunner = new ClangdRunner(logger, dataFolderName, scopeDir, clangdCompileCommandsDir);
		await this._clangdRunner.initialize();
	}

	/**
	 * 获取ClangdRunner实例
	 */
	private async getClangdRunner(scopeDir: URI, clangdCompileCommandsDir?: string): Promise<ClangdRunner | undefined> {
		if (!this._clangdRunner) {
			this.initialize(scopeDir, clangdCompileCommandsDir);
		}
		return this._clangdRunner;
	}

	async getSymbolReferences(scopeDir: URI, symbolReferencesQuery: SymbolReferencesQuery): Promise<SymbolReference[]> {
		const clangdRunner = await this.getClangdRunner(scopeDir);
		if (!clangdRunner) {
			throw new Error('ClangdRunner is not initialized');
		}
		const resultPromises = await clangdRunner.getSymbolReferences(symbolReferencesQuery);
		const results = await Promise.all(resultPromises);
		return results.flat();
	}

	async didOpen(scopeDir: URI, documentUri: string): Promise<void> {
		const clangdRunner = await this.getClangdRunner(scopeDir);
		if (!clangdRunner) {
			throw new Error('ClangdRunner is not initialized');
		}
		await clangdRunner.didOpen(documentUri);
	}
}

// 注册为单例服务
registerSingleton(IClangdMainService, ClangdMainService, InstantiationType.Eager);
