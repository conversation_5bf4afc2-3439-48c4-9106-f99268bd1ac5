import { execFile as _execFile, ChildProcessWithoutNullStreams, spawn } from 'node:child_process';
import * as os from 'os';
import * as path from 'path';
import { JSONRPCEndpoint, LspClient } from 'ts-lsp-client';
import { pathToFileURL } from 'url';
import { promisify } from 'util';
import { IDisposable } from '../../../../../base/common/lifecycle.js';
import { URI } from '../../../../../base/common/uri.js';
import { ILogger } from '../../../../../platform/log/common/log.js';
import { fileExists } from '../utils/common.js';
import { findClangdPath } from './findClangd.js';
import { SymbolReferencesProvider, SymbolReferencesQuery } from '../codebase/symbolReferences.js';
const execFile = promisify(_execFile);


export type FileURI = Omit<URI, 'fsPath'> & {
	scheme: 'file'
	/**
	 * 文件系统路径
	 */
	fsPath: string
}

export function isFileURI(uri: URI): uri is FileURI {
	return uri.scheme === 'file';
}

export class ClangdRunner implements IDisposable {
	public dispose(): void {
		this.terminateProcess();
		if (this.client) {
			this.client.exit();
		}
	}
	// private indexRoot: FileURI = undefined!;
	private client: LspClient = undefined!;
	private referencesProvider: SymbolReferencesProvider = undefined!;
	private _process: ChildProcessWithoutNullStreams | undefined;

	constructor(
		private logger: ILogger,
		private dataFolderName: string,
		private scopeDir: URI,
		private compileCommandsDir?: string
	) {
	}

	public async initialize() {
		// 如果已有进程在运行，先终止它
		this.terminateProcess();

		// 如果有客户端在运行，让它退出
		if (this.client) {
			try {
				this.client.exit();
			} catch (error) {
				this.logger.warn('clangd', `Error exiting client: ${error}`);
			}
			this.client = undefined!;
		}

		// 重置引用提供者
		this.referencesProvider = undefined!;

		const globalStoragePath = path.join(os.homedir(), this.dataFolderName, 'clangd', 'indexroot');
		const indexRoot = URI.file(globalStoragePath);

		if (!isFileURI(indexRoot)) {
			throw new Error('ctags only supports running on the file system');
		}
		// this.indexRoot = indexRoot;
		try {
			const clangdPath = await findClangdPath(this.logger, this.dataFolderName);
			if (!clangdPath) {
				this.logger.error('clangd', 'Clangd executable not found. Please install Clangd manually.');
				return;
			}

			// 检查 compile_commands.json 文件是否存在
			const compileCommandsPath = path.join(this.scopeDir.fsPath, 'build', 'compile_commands.json');

			if (!await fileExists(compileCommandsPath)) {
				this.logger.warn('clangd', `Warning: compile_commands.json not found at ${compileCommandsPath}`);
			} else {
				this.logger.info('clangd', `Using compile_commands.json from ${compileCommandsPath}`);
			}

			// 记录 clangd 版本信息
			try {
				const versionOutput = (await execFile(clangdPath, ['--version'])).stdout;
				this.logger.info('clangd', `Using clangd version: ${versionOutput.trim()}`);
			} catch (err) {
				this.logger.warn('clangd', `Failed to get clangd version: ${err}`);
			}

			// 如果传入了compileCommandsDir配置，则使用它；否则使用默认目录
			const compileCommandsDir = this.compileCommandsDir && this.compileCommandsDir.trim() !== ''
				? this.compileCommandsDir
				: `${this.scopeDir.fsPath}/build`;

			this.logger.info('clangd', `Using compile commands directory: ${compileCommandsDir}`);

			const clangdArguments = [
				// 使用配置的compile commands目录
				`--compile-commands-dir=${compileCommandsDir}`,
				"--background-index",
				"--completion-style=detailed",
				"--header-insertion=never",
				"-log=info",
				"-pretty"
			];

			this.logger.info('clangd', `Starting clangd with arguments: ${clangdArguments.join(' ')}`);

			this._process = spawn(
				clangdPath,
				clangdArguments,
				{
					shell: true,
					stdio: 'pipe'
				}
			);

			const process = this._process;

			// 记录进程ID
			if (process.pid) {
				this.logger.info('clangd', `Clangd process started with PID: ${process.pid}`);
			}

			// 添加进程事件监听
			process.on('error', (err) => {
				this.logger.error('clangd', `Clangd process error: ${err.message}`);
			});

			process.on('exit', (code, signal) => {
				this.logger.info('clangd', `Clangd process exited with code ${code} and signal ${signal}`);
			});

			// 添加进程断开连接监听
			process.on('disconnect', () => {
				this.logger.warn('clangd', 'Clangd process disconnected');
			});

			// 添加进程消息监听
			process.on('message', (message) => {
				this.logger.info('clangd', `Clangd process message: ${JSON.stringify(message)}`);
			});

			// 监听 stdout 输出
			process.stdout.on('data', (data) => {
				const message = data.toString();
				// LSP消息通常是JSON格式，这里简单检测是否包含JSON
				if (message.includes('"jsonrpc"')) {
					this.logger.trace('clangd-lsp', `${message}`);
				} else {
					this.logger.info('clangd-stdout', `${message}`);
				}
			});

			// 添加stderr监听处理诊断输出
			process.stderr.on('data', (data) => {
				const message = data.toString();

				// 根据消息内容分类处理不同级别的日志
				if (message.includes('error:') || message.includes('Error:')) {
					this.logger.error('clangd', `Clangd stderr: ${message}`);
				} else if (message.includes('warning:') || message.includes('Warning:')) {
					this.logger.warn('clangd', `Clangd stderr: ${message}`);
				} else if (message.startsWith('I[')) {
					// clangd的信息日志通常以I[时间戳]开头
					this.logger.info('clangd', `Clangd stderr: ${message}`);
				} else if (message.startsWith('E[')) {
					// clangd的错误日志通常以E[时间戳]开头
					this.logger.error('clangd', `Clangd stderr: ${message}`);
				} else if (message.startsWith('W[')) {
					// clangd的警告日志通常以W[时间戳]开头
					this.logger.warn('clangd', `Clangd stderr: ${message}`);
				} else {
					// 对于其他未分类的输出，使用trace级别记录，避免错误日志污染
					this.logger.trace('clangd', `Clangd stderr: ${message}`);
				}
			});

			// 注册进程终止处理器
			process.on('SIGTERM', () => this.terminateProcess());
			process.on('SIGINT', () => this.terminateProcess());

			let client: LspClient;
			const endpoint: JSONRPCEndpoint = new JSONRPCEndpoint(
				process.stdin,
				process.stdout,
			);
			client = new LspClient(endpoint);
			this.client = client;

			if (!process.pid) {
				this.logger.error('clangd', 'Clangd process not started');
				return;
			}
			// 启动客户端
			const result = await this.client.initialize({
				processId: process.pid,
				capabilities: {},
				clientInfo: {
					name: 'clangd-language-client',
					version: '0.0.1'
				},
				workspaceFolders: [
					{
						name: 'workspace',
						uri: pathToFileURL(this.scopeDir.fsPath).href
					}
				],
				rootUri: null,
				initializationOptions: {
					clangdFileStatus: true
				}
			});
			if (!result) {
				this.logger.error('clangd', 'Failed to initialize clangd client: No result returned');
				return;
			}

			// 通知服务器初始化已完成
			await this.client.initialized();

			this.logger.info('clangd', 'Clangd Language Server started', result);


			// 初始化引用查询提供者
			this.referencesProvider = new SymbolReferencesProvider(this.client, this.logger);
		} catch (error) {
			this.logger.error(`Error starting Clangd Language Server: ${error}`);
		}
	}

	/**
	 * 终止当前正在运行的 clangd 进程
	 */
	private terminateProcess(): void {
		if (this._process && !this._process.killed) {
			try {
				this._process.kill();
				this.logger.info('clangd', 'Clangd process terminated');
			} catch (err) {
				this.logger.error('clangd', `Error terminating clangd process: ${err}`);
			}
			this._process = undefined;
		}
	}

	/**
	 * 查找符号引用
	 * @param symbolReferencesQuery 符号引用查询对象
	 * @param symbolReferencesQuery.file 文件URI
	 * @param symbolReferencesQuery.line 行号
	 * @param symbolReferencesQuery.character 字符位置
	 * @returns 符号引用数组
	 */
	public async getSymbolReferences(symbolReferencesQuery: SymbolReferencesQuery) {
		if (!this.referencesProvider) {
			this.logger.error('ClangdRunner', 'References provider not initialized');
			throw new Error('引用提供者未初始化');
		}
		return await this.referencesProvider.findReferencesAt(symbolReferencesQuery);
	}

	/**
	 * 获取文档的符号引用
	 * @param documentUri 文档URI
	 * @returns 符号引用数组
	 */
	public async didOpen(documentUri: string) {
		if (!this.referencesProvider) {
			this.logger.error('ClangdRunner', 'References provider not initialized');
			throw new Error('引用提供者未初始化');
		}
		await this.referencesProvider.didOpen(documentUri);
	}
}
