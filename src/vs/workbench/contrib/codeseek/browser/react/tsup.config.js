/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { defineConfig } from 'tsup'
import { cpSync } from 'fs'
import fs from 'fs'
import { join } from 'path'

export default defineConfig({
	entry: [
		'./src2/sidebar-tsx/index.tsx',
		'./src2/uaclogin/index.tsx',
		'./src2/codeseek-settings-tsx/index.tsx',
		'./src2/quick-edit-tsx/index.tsx',
		'./src2/diff/index.tsx',
		'./src2/shiki/index.tsx',
	],
	outDir: '../../../../../../../out/vs/workbench/contrib/codeseek/browser/react/out',
	format: ['esm'],
	splitting: false,

	// dts: true,
	sourcemap: true,

	clean: false,
	platform: 'browser', // 'node'
	target: 'esnext',
	injectStyle: true, // bundle css into the output file
	outExtension: () => ({ js: '.js' }),
	// default behavior is to take local files and make them internal (bundle them) and take imports like 'react' and leave them external (don't bundle them), we want the opposite in many ways
	noExternal: [ // noExternal means we should take these things and make them not external (bundle them into the output file) - anything that doesn't start with a "." needs to be force-flagged as not external
		/^(?!\.).*$/
	],
	external: [ // these imports should be kept external ../../../ are external (this is just an optimization so the output file doesn't re-implement functions)
		new RegExp('../../../*.js'
			.replaceAll('.', '\\.')
			.replaceAll('*', '.*'))
	],
	treeshake: false,
	esbuildOptions(options) {
		options.outbase = 'src2'  // tries copying the folder hierarchy starting at src2
	},
	onSuccess() {
		// 在构建成功后，将输出目录复制到项目根路径的指定目录
		const fromDir = join(__dirname, '../../../../../../../out/vs/workbench/contrib/codeseek/browser/react/out')

		cpSync(fromDir, './out', { recursive: true, force: true })

		// // 添加打印信息
		console.log('构建成功，输出目录已复制到: out')
	}
})
