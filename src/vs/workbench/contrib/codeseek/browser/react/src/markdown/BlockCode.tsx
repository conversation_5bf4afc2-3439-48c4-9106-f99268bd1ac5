/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import React, { useLayoutEffect, useEffect } from "react";
import { CodeseekCodeEditorProps } from "../util/inputs.js";
import { useAccessor } from "../util/services.js";
import { URI } from '../../../../../../../base/common/uri.js';
import { VSCodeFileIcon } from '../sidebar-tsx/SidebarChat.js';
import { FileKind } from '../../../../../../../platform/files/common/files.js';
import * as path from '../../../../../../../base/common/path.js';
import { getWorkspaceUri } from '../../../../common/helpers/path.js';

function FileHeader({ uri, apply, title }: { uri: URI, apply?: boolean, title?: string }) {
	const applyState = apply ? (
		<>
			<div className="text-xs ml-2">Applying</div>
			<span className="codicon codicon-loading animate-spin ml-2" style={{ fontSize: '10px' }} />
		</>
	) : null;
	const fileName = uri.fsPath.split(/[\\/]/).pop() || "";
	const accessor = useAccessor();
	const editCodeService = accessor.get('IEditCodeService');

	const openFile = async () => {
		await editCodeService.openOrCreateFile(uri);
	};

	return (
		<div className="flex items-center hover:cursor-pointer">
			<div className="flex items-center" onClick={openFile} title={title}>
				<VSCodeFileIcon uri={uri} fileKind={FileKind.FILE} />
				<span className="text-xs">{fileName}</span>
			</div>
			{applyState}
		</div>
	);
}

export const BlockCode = ({
	buttonsOnHover,
	uri,
	applying,
	tokenId,
	...codeEditorProps
}: {
	buttonsOnHover?: React.ReactNode;
	uri?: URI;
	applying?: boolean;
	tokenId: string;
} & CodeseekCodeEditorProps) => {
	const [html, setHtml] = React.useState<string>('');
	const accessor = useAccessor();
	const workspaceService = accessor.get('IWorkspaceContextService');
	const exporerService = accessor.get('ICodeSeekExporerService');
	const maxHeight = codeEditorProps.maxHeight? 'max-h-[' + codeEditorProps.maxHeight + 'px]' : ''

	const generateHtml = React.useCallback(async () => {
		const trustedHtml = await exporerService.codeToHtml(codeEditorProps.initValue, codeEditorProps.language ?? 'plaintext');
		setHtml(trustedHtml as unknown as string);
	}, [exporerService, codeEditorProps.initValue, codeEditorProps.language]);

	useLayoutEffect(() => {
		generateHtml();
	}, [generateHtml]);

	useEffect(() => {
		const disposable = exporerService.onHighlighterChange(() => {
			generateHtml();
		});
		return () => disposable.dispose();
	}, [exporerService, generateHtml]);

	const workspaceUri = getWorkspaceUri(workspaceService).workspaceUri;
	const relativePath = uri && workspaceUri ? path.relative(workspaceUri.fsPath, uri.fsPath) : '';
	const codeEditroHeader = uri ? (
		<div className="flex items-center justify-between pl-1 pr-2 border-b border-[var(--vscode-commandCenter-inactiveBorder)] text-md">
			<FileHeader uri={uri} apply={applying} title={relativePath} />
			{buttonsOnHover && (
				<div className="flex space-x-1">
					{buttonsOnHover}
				</div>
			)}
		</div>
	) : null;
	return (
		<>
			<div key={tokenId} className="my-1.5 group border border-[var(--vscode-commandCenter-inactiveBorder)] rounded-md">
				{codeEditroHeader}
				<div className={`relative w-full overflow-auto rounded-md font-mono font-normal text-[12px] leading-[16px] tracking-normal ${maxHeight}`}>
					{html && <div dangerouslySetInnerHTML={{ __html: html }} />}
				</div>
			</div>
		</>
	);
};
