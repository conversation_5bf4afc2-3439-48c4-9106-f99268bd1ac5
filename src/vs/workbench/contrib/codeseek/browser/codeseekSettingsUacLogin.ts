/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';
import { EditorInput } from '../../../common/editor/editorInput.js';
import * as nls from '../../../../nls.js';
import { EditorExtensions } from '../../../common/editor.js';
import { EditorPane } from '../../../browser/parts/editor/editorPane.js';
import { IEditorGroup } from '../../../services/editor/common/editorGroupsService.js';
import { ITelemetryService } from '../../../../platform/telemetry/common/telemetry.js';
import { IThemeService } from '../../../../platform/theme/common/themeService.js';
import { IStorageService } from '../../../../platform/storage/common/storage.js';
import { Dimension } from '../../../../base/browser/dom.js';
import { EditorPaneDescriptor, IEditorPaneRegistry } from '../../../browser/editor.js';
import { SyncDescriptor } from '../../../../platform/instantiation/common/descriptors.js';
import { Action2, MenuId, registerAction2 } from '../../../../platform/actions/common/actions.js';
import { Registry } from '../../../../platform/registry/common/platform.js';
import { ServicesAccessor } from '../../../../editor/browser/editorExtensions.js';
import { IEditorService } from '../../../services/editor/common/editorService.js';
import { URI } from '../../../../base/common/uri.js';


import { mountCodeseekUacLogin } from './react/out/uaclogin/index.js';
import { Codicon } from '../../../../base/common/codicons.js';
import { IDisposable } from '../../../../base/common/lifecycle.js';
import { ContextKeyExpr } from '../../../../platform/contextkey/common/contextkey.js';
import { UserStatus } from '../common/uac/UacloginTypes.js';


// refer to preferences.contribution.ts keybindings editor


export const ZTE_USER_LOGIN_STATE_KEY = 'zteUserLogin';
class CodeseekUacLoginInput extends EditorInput {

	static readonly ID: string = 'workbench.input.codeseek.uacLogin';

	static readonly RESOURCE = URI.from({ // I think this scheme is invalid, it just shuts up TS
		scheme: 'codeseek',  // Custom scheme for our editor (try Schemas.https)
		path: 'uacLogin'
	});
	readonly resource = CodeseekUacLoginInput.RESOURCE;

	constructor() {
		super();
	}

	override get typeId(): string {
		return CodeseekUacLoginInput.ID;
	}

	override getName(): string {
		return nls.localize('codeseekUacLoginInputsName', 'ZTE用户统一认证');
	}

	override getIcon() {
		return Codicon.checklist; // symbol for the actual editor pane
	}

}


class CodeseekUacLoginPane extends EditorPane {
	static readonly ID = 'workbench.test.uacLoginPane';

	constructor(
		group: IEditorGroup,
		@ITelemetryService telemetryService: ITelemetryService,
		@IThemeService themeService: IThemeService,
		@IStorageService storageService: IStorageService,
		@IInstantiationService private readonly instantiationService: IInstantiationService,
	) {
		super(CodeseekUacLoginPane.ID, group, telemetryService, themeService, storageService);
	}

	protected createEditor(parent: HTMLElement): void {
		parent.style.height = '100%';
		parent.style.width = '100%';

		const uacLoginElt = document.createElement('div');
		uacLoginElt.style.height = '100%';
		uacLoginElt.style.width = '100%';

		parent.appendChild(uacLoginElt);

		// Mount React into the scrollable content
		this.instantiationService.invokeFunction(accessor => {
			const disposables: IDisposable[] | undefined = mountCodeseekUacLogin(uacLoginElt, accessor);

			disposables?.forEach(d => this._register(d));
		});
	}

	layout(dimension: Dimension): void {
	}


	override get minimumWidth() { return 700; }

}

// register UacLogin pane
Registry.as<IEditorPaneRegistry>(EditorExtensions.EditorPane).registerEditorPane(
	EditorPaneDescriptor.create(CodeseekUacLoginPane, CodeseekUacLoginPane.ID, nls.localize('CodeseekUacLoginPane', "ZTE用户统一认证")),
	[new SyncDescriptor(CodeseekUacLoginInput)]
);

export const CODESEEK_OPEN_UAC_LOGIN_ACTION_ID = 'workbench.action.openCodeseekUacLogin';
registerAction2(class extends Action2 {
	constructor() {
		super({
			id: CODESEEK_OPEN_UAC_LOGIN_ACTION_ID,
			title: nls.localize2('codeseekUacLogin', "ZTE用户统一认证:登陆"),
			f1: true,
			icon: Codicon.logIn,
			menu: [
				{
					id: MenuId.AccountsContext,
					group: 'zte_uac',
					when: ContextKeyExpr.equals(ZTE_USER_LOGIN_STATE_KEY, UserStatus.logout),
				}
			]
		});
	}
	async run(accessor: ServicesAccessor): Promise<void> {
		const editorService = accessor.get(IEditorService);
		const instantiationService = accessor.get(IInstantiationService);

		// close all instances if found
		const openEditors = editorService.findEditors(CodeseekUacLoginInput.RESOURCE);
		if (openEditors.length > 0) {
			await editorService.closeEditors(openEditors);
		}

		// then, open one single editor
		const input = instantiationService.createInstance(CodeseekUacLoginInput);
		await editorService.openEditor(input);
	}
});

