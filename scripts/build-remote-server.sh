#!/usr/bin/env bash

# 脚本用于构建flow远程服务器组件

set -e

cur_dir=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
root_dir=$(cd "$cur_dir/.." && pwd)
vscode_out_dir="$root_dir/../VSCode-linux-x64"
extensions_dir="$vscode_out_dir/resources/app/extensions"
cd "$root_dir"

# 获取版本
VERSION=$(node -p "require('./package.json').version")
# 获取产品发布版本
RELEASE=$(node -p "require('./package.json').productVersion")
# 获取commit信息
COMMIT=$(git rev-parse HEAD)
PLATFORMS=("linux-x64" "win32-x64") # "linux-arm64" "darwin-x64" "darwin-arm64" "win32-x64"

# 清理构建目录
rm -rf .build/remote-server
mkdir -p .build/remote-server

# 检查编译后的文件
echo "检查服务器输出文件..."
if [ ! -f "out/server-main.js" ]; then
  echo "错误: 服务器主入口文件 out/server-main.js 不存在"
  exit 1
fi

# 创建版本发布目录
VERSION_RELEASE_DIR=".build/remote-server/${VERSION}-${RELEASE}"
mkdir -p "${VERSION_RELEASE_DIR}"

for PLATFORM in "${PLATFORMS[@]}"; do
  echo "构建 $PLATFORM 远程服务器..."

  # 解析操作系统和架构
  OS=$(echo $PLATFORM | cut -d'-' -f1)
  ARCH=$(echo $PLATFORM | cut -d'-' -f2)

  # 创建输出目录
  OUTDIR=".build/remote-server/flow-server-$VERSION-$PLATFORM"
  mkdir -p "$OUTDIR/bin"
  mkdir -p "$OUTDIR/out"
  mkdir -p "$OUTDIR/resources/server"
	mkdir -p "$OUTDIR/extensions"

  # 复制服务器文件
  echo "复制服务器文件..."
  cp -r out-build/vs "$OUTDIR/out/"
  cp out-build/server-main.js "$OUTDIR/out/"
	cp out-build/server-cli.js "$OUTDIR/out/"
	cp out-build/bootstrap-*.js "$OUTDIR/out/"
	cp out-build/nls.messages.json "$OUTDIR/out/"
  cp product.json "$OUTDIR/"
  cp resources/server/favicon.ico "$OUTDIR/resources/server/"
  cp -rf "$extensions_dir"/* "$OUTDIR/extensions/"


  NODE_PATH=$HOME/.cache/node
	if [ -f "$NODE_PATH" ]; then
		cp "$NODE_PATH" "$OUTDIR/node"
		chmod +x "$OUTDIR/node"
	else
		echo "错误: 未找到v20.18.1版本的nodejs，跳过复制node"
		exit 1
	fi

	# 复制依赖
  echo "复制依赖..."
  mkdir -p "$OUTDIR/node_modules"
  if [ -d "remote/node_modules" ]; then
    cp -r remote/node_modules/* "$OUTDIR/node_modules/"
		# fix: /lib64/libstdc++.so.6: version `GLIBCXX_3.4.26' not found
		[ -f "$HOME/.cache/spdlog.node" ] && cp $HOME/.cache/spdlog.node "$OUTDIR/node_modules/@vscode/spdlog/build/Release/"
  else
    echo "警告: remote/node_modules 目录不存在，跳过复制依赖"
    # 创建基本依赖目录结构
    mkdir -p "$OUTDIR/node_modules/@vscode"
  fi

  # 向product.json中添加version和commit字段
  jq '. + { "version": "'"$VERSION"'", "commit": "'"$COMMIT"'" }' product.json > "$OUTDIR/product.json"

  mkdir -p "$OUTDIR/out/vs/code/browser/workbench"
  cat > "$OUTDIR/out/vs/code/browser/workbench/workbench.css" << 'EOF'
/* Minimal workbench.css */
body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  margin: 0;
  padding: 0;
  background-color: #1e1e1e;
  color: #cccccc;
}
.monaco-workbench {
  width: 100%;
  height: 100%;
}
EOF

  # 创建package.json
  cat > "$OUTDIR/package.json" << EOF
{
  "name": "flow-server",
  "version": "$VERSION",
  "type": "module",
  "private": true
}
EOF

  # 创建启动脚本
  echo "创建启动脚本..."
  if [[ $PLATFORM == win32* ]]; then
    # Windows版本
    echo "@echo off
set NODE=%~dp0\\node.exe
if not exist \"%NODE%\" (
  set NODE=node
)
\"%NODE%\" \"%~dp0\\..\\out\\server-main.js\" %*" > "$OUTDIR/bin/flow-server.cmd"

    # 打包为zip格式(Windows)
    echo "打包 $PLATFORM 远程服务器..."
    PACKAGE_NAME="flow-reh-${OS}-${ARCH}-${VERSION}.${RELEASE}.zip"
    (cd .build/remote-server && zip -r "${PACKAGE_NAME}" "flow-server-$VERSION-$PLATFORM")
    # 移动到版本发布目录
    mv ".build/remote-server/${PACKAGE_NAME}" "${VERSION_RELEASE_DIR}/"
  else
    # Unix版本
    cat > "$OUTDIR/bin/flow-server" << 'EOF'
#!/bin/bash
DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
NODE="$DIR/../node"
if [ ! -e "$NODE" ]; then
  NODE=node
fi

# 自动创建必要的目录
SERVER_DATA_DIR="${HOME}/.flow-server"
mkdir -p "${SERVER_DATA_DIR}/data/User"
mkdir -p "${SERVER_DATA_DIR}/data/logs"
mkdir -p "${SERVER_DATA_DIR}/data/Machine"
mkdir -p "${SERVER_DATA_DIR}/extensions"
if [ ! -f "${SERVER_DATA_DIR}/extensions/extensions.json" ]; then
  echo "[]" > "${SERVER_DATA_DIR}/extensions/extensions.json"
fi

# 启动服务器
"$NODE" --experimental-modules --no-warnings "$DIR/../out/server-main.js" "$@"
EOF
    chmod +x "$OUTDIR/bin/flow-server"

    # 打包为tar.gz格式(Unix)
    echo "打包 $PLATFORM 远程服务器..."
    PACKAGE_NAME="flow-reh-${OS}-${ARCH}-${VERSION}.${RELEASE}.tar.gz"
    (cd .build/remote-server && tar -czf "${PACKAGE_NAME}" "flow-server-$VERSION-$PLATFORM")
    # 移动到版本发布目录
    mv ".build/remote-server/${PACKAGE_NAME}" "${VERSION_RELEASE_DIR}/"
  fi
done

echo "flow-server build success, please check ${VERSION_RELEASE_DIR}/"
