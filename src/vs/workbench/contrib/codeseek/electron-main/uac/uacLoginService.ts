/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { Disposable } from '../../../../../base/common/lifecycle.js';
import { registerSingleton, InstantiationType } from '../../../../../platform/instantiation/common/extensions.js';


import { ZTE_USER_INFO_KEY } from '../../common/storageKeys.js';
import { createDecorator } from '../../../../../platform/instantiation/common/instantiation.js';
import { Emitter, Event } from '../../../../../base/common/event.js';
import { CancellationToken } from '../../../../../base/common/cancellation.js';
import { codeServerPwdFree, loginWithPwd, pollUac, queryUserInfo, udsPwdFree } from './utilities/uac.js';
import { IRequestService } from '../../../../../platform/request/common/request.js';
import { problemCheck } from './utilities/check.js';
import { AuthInfo, UserInfo, UserStatus } from '../../common/uac/UacloginTypes.js';
import { IApplicationStorageMainService } from '../../../../../platform/storage/electron-main/storageMainService.js';
import { StorageScope, StorageTarget } from '../../../../../platform/storage/common/storage.js';

export interface IUacLoginService {
	_serviceBrand: undefined;
	udsLogin(): Promise<void>;
	codeServerLogin(cookie: string): Promise<void>;
	getUserInfo(): Promise<UserInfo | undefined>;
	logout(): Promise<void>
	login(authInfo: AuthInfo): Promise<boolean>;
	qrLogin(dataStr: string, cancellationToken?: CancellationToken): Promise<void>;
	problemCheck(httpProxy: string, httpsProxy: string): Promise<string[]>;
	onDidChangeUserInfo: Event<UserInfo>;
	onDidUserLogin: Event<UserInfo>;
	onDidUserLogout: Event<UserInfo>;
	onOpenUacLoginPage: Event<void>;
}

export const IUacLoginService = createDecorator<IUacLoginService>('uacLoginService');

export class UacLoginService extends Disposable implements IUacLoginService {
	_serviceBrand: undefined;

	private readonly _onDidChangeUserInfo = new Emitter<UserInfo>();
	private readonly _onDidUserLogin = new Emitter<UserInfo>();
	private readonly _onDidUserLogout = new Emitter<UserInfo>;
	private readonly _onOpenUacLoginPage = new Emitter<void>();

	onDidChangeUserInfo: Event<UserInfo> = this._onDidChangeUserInfo.event;
	onDidUserLogin: Event<UserInfo> = this._onDidUserLogin.event;
	onDidUserLogout: Event<UserInfo> = this._onDidUserLogout.event;
	onOpenUacLoginPage: Event<void> = this._onOpenUacLoginPage.event;
	private userInfo: UserInfo | undefined;


	constructor(
		@IApplicationStorageMainService private readonly storageService: IApplicationStorageMainService,
		@IRequestService private readonly requestService: IRequestService,
	) {
		super();
		const kaTimer = setInterval(() => {
			this.refreshUserSession()
			clearInterval(kaTimer)
		}, 2000);
	}


	private succLoginFunc(result: any) {
		const userInfo = { userId: result.userId, username: result.username, department: result.department, userStatus: UserStatus.login, token: result.token, gerritHttpPassword: result.gerritHttpPassword, trackInfo: result.trackInfo };
		this.setUserInfo(userInfo)

		this._onDidUserLogin.fire(userInfo);
	}

	public async udsLogin(): Promise<void> {
		const result: any = await udsPwdFree(this.requestService)
		if (!result.error) {
			this.succLoginFunc(result);
		}

	}

	public async codeServerLogin(cookies: string): Promise<void> {
		const result: any = await codeServerPwdFree(this.requestService, cookies)

		if (!result.error) {
			this.succLoginFunc(result);
		}
	}

	public async qrLogin(dataStr: string, cancellationToken?: CancellationToken): Promise<any> {
		const result: any = await pollUac(this.requestService, dataStr, cancellationToken);

		if (!result.error) {
			this.succLoginFunc(result);
		}
		return result;
	}

	public async login(authInfo: AuthInfo): Promise<any> {
		const result: any = await loginWithPwd(this.requestService, authInfo.userId, authInfo.password, authInfo.dynPwd)

		if (!result.error) {
			this.succLoginFunc(result);
		}
		return result
	}

	public async logout(): Promise<void> {
		if (this.userInfo) {

			this.userInfo.userStatus = UserStatus.logout
			this.setUserInfo(this.userInfo)
			this._onDidUserLogout.fire(this.userInfo)
		}
	}

	public async getUserInfo(): Promise<UserInfo | undefined> {
		await this.refreshUserSession()
		return this.userInfo;
	}

	public setUserInfo(userInfo: UserInfo) {
		if (this.isUserInfoEqual(this.userInfo, userInfo)) {
			return
		}
		this.userInfo = userInfo;

		this._onDidChangeUserInfo.fire(this.userInfo)

		this.saveUserInfo(userInfo)

	}

	private async refreshUserSession() {
		const userSessionInfo: UserInfo | undefined = this.loadUserInfo();

		if (userSessionInfo && userSessionInfo.userId && userSessionInfo.token && userSessionInfo.userStatus == UserStatus.login) {
			try {
				await queryUserInfo(this.requestService, userSessionInfo.userId, userSessionInfo.token)
				this.setUserInfo(userSessionInfo);
			} catch (error) {
				this.removeUserInfo()
				await this.openUacLogin();
			}
		} else {
			await this.openUacLogin();
		}

	}

	private async openUacLogin() {
		this._onOpenUacLoginPage.fire();
	}

	public async problemCheck(httpProxy: string, httpsProxy: string) {
		const results: string[] = await problemCheck(httpProxy, httpsProxy)
		return results;
	}
	private saveUserInfo(userInfo: UserInfo) {
		this.storageService.store(ZTE_USER_INFO_KEY, JSON.stringify(userInfo), StorageScope.APPLICATION, StorageTarget.USER)
	}
	private loadUserInfo() {
		const userInfoStr = this.storageService.get(ZTE_USER_INFO_KEY, StorageScope.APPLICATION)
		if (userInfoStr) {
			return JSON.parse(userInfoStr);
		}
	}

	private removeUserInfo() {
		this.storageService.remove(ZTE_USER_INFO_KEY, StorageScope.APPLICATION);
	}

	private isUserInfoEqual(oldUserInfo: UserInfo | undefined, newUserInfo: UserInfo | undefined): boolean {
		if (oldUserInfo === newUserInfo) return true; // 如果两个引用相同
		if (!oldUserInfo || !newUserInfo) return false; // 如果其中一个是 undefined

		return oldUserInfo.userId === newUserInfo.userId &&
			oldUserInfo.username === newUserInfo.username &&
			oldUserInfo.department === newUserInfo.department &&
			oldUserInfo.userStatus === newUserInfo.userStatus &&
			oldUserInfo.token === newUserInfo.token &&
			oldUserInfo.gerritHttpPassword === newUserInfo.gerritHttpPassword &&
			this.isTrackInfoEqual(oldUserInfo.trackInfo, newUserInfo.trackInfo);
	}

	private isTrackInfoEqual(oldTrackInfo?: { department?: string; id?: string; workplace?: string; }, newTrackInfo?: { department?: string; id?: string; workplace?: string; }): boolean {
		if (oldTrackInfo === newTrackInfo) return true; // 如果两个引用相同
		if (!oldTrackInfo || !newTrackInfo) return false; // 如果其中一个是 undefined

		return oldTrackInfo.department === newTrackInfo.department &&
			oldTrackInfo.id === newTrackInfo.id &&
			oldTrackInfo.workplace === newTrackInfo.workplace;
	}
}


registerSingleton(IUacLoginService, UacLoginService, InstantiationType.Eager);
