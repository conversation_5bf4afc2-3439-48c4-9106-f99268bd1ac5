import { CancellationToken } from '../../../../base/common/cancellation.js';
import { URI } from '../../../../base/common/uri.js';


export enum AskReponseType {
	yesButtonClicked = 'yesButtonClicked',
	noButtonClicked = 'noButtonClicked',
	messageResponse = 'messageResponse',
}

export type AskResponse = {
	type: string;
	response?: AskReponseType;
	text?: string;
};

export type InternalToolInfo = {
	name: string;
	description: string;
	params: {
		[paramName: string]: { type: string; description: string | undefined }; // name -> type
	};
	required: string[]; // required paramNames
	needApprove: boolean;
};

// 定义工具名称枚举
export enum ToolNameEnum {
	READ_FILE = 'read_file',
	LIST_FILES = 'list_files',
	PATHNAME_SEARCH = 'pathname_search',
	SEARCH = 'search',
	CREATE_FILE = 'create_file',
	UPDATE_TO_FILE = 'update_to_file',
	APPROVE_REQUEST = 'approve_request',
	ASK_FOLLOWUP_QUESTION = 'ask_followup_question',
	CTAGS_QUERY = 'ctags_query',
	CLANGD_QUERY = 'clangd_query',
	SHOW_SUMMARY = 'show_summary',
	EXEC_COMMAND = 'exec_command',
}

const paginationHelper = {
	desc: `Very large results may be paginated (indicated in the result). Pagination fails gracefully if out of bounds or invalid page number.`,
	param: { pageNumber: { type: 'number', description: 'The page number (optional, default is 1).' }, }
} as const;

export const codeseekTools = {
	[ToolNameEnum.READ_FILE]: {
		name: ToolNameEnum.READ_FILE,
		description: `Returns file contents of a given URI. ${paginationHelper.desc}`,
		params: {
			uri: { type: 'string', description: undefined },
		},
		required: ['uri'],
		needApprove: false
	},

	[ToolNameEnum.LIST_FILES]: {
		name: ToolNameEnum.LIST_FILES,
		description: `Returns all file names and folder names in a given URI. ${paginationHelper.desc}`,
		params: {
			uri: { type: 'string', description: undefined },
			...paginationHelper.param
		},
		required: ['uri'],
		needApprove: false
	},

	[ToolNameEnum.PATHNAME_SEARCH]: {
		name: ToolNameEnum.PATHNAME_SEARCH,
		description: `Returns all pathnames that match a given grep query. You should use this when looking for a file with a specific name or path. This does NOT search file content. ${paginationHelper.desc}`,
		params: {
			query: { type: 'string', description: undefined },
			...paginationHelper.param,
		},
		required: ['query'],
		needApprove: false
	},

	[ToolNameEnum.SEARCH]: {
		name: ToolNameEnum.SEARCH,
		description: `Returns all code excerpts containing the given string or grep query. This does NOT search pathname. As a follow-up, you may want to use read_file to view the full file contents of the results. ${paginationHelper.desc}`,
		params: {
			query: { type: 'string', description: undefined },
			...paginationHelper.param,
		},
		required: ['query'],
		needApprove: false
	},

	[ToolNameEnum.CREATE_FILE]: {
		name: ToolNameEnum.CREATE_FILE,
		description: `Creates a new file at the given URI with the given content.`,
		params: {
			uri: { type: 'string', description: undefined },
			content: { type: 'string', description: undefined },
		},
		required: ['uri', 'content'],
		needApprove: true
	},

	[ToolNameEnum.UPDATE_TO_FILE]: {
		name: ToolNameEnum.UPDATE_TO_FILE,
		description: `Updates the file at the given URI with the given content.`,
		params: {
			uri: { type: 'string', description: undefined },
			content: { type: 'string', description: undefined },
			start: { type: 'number', description: undefined },
			end: { type: 'number', description: undefined },
		},
		required: ['uri', 'content', 'start', 'end'],
		needApprove: true
	},

	[ToolNameEnum.APPROVE_REQUEST]: {
		name: ToolNameEnum.APPROVE_REQUEST,
		description: `Attemps to complete the given text at the given position in the given file.`,
		params: {
			result: { type: 'string', description: 'The result from a previous attempt at completion.' },
			command: { type: 'string', description: 'Command to demonstrate result (optional)' },
		},
		required: ['result'],
		needApprove: false
	},

	[ToolNameEnum.ASK_FOLLOWUP_QUESTION]: {
		name: ToolNameEnum.ASK_FOLLOWUP_QUESTION,
		description: `Asks a follow-up question to the user.`,
		params: {
			question: { type: 'string', description: 'The question to ask the user.' },
		},
		required: ['question'],
		needApprove: false
	},

	[ToolNameEnum.CTAGS_QUERY]: {
		name: ToolNameEnum.CTAGS_QUERY,
		description: `Use the ctags tool to output the file path, type, and starting line and column number of a given code symbol.`,
		params: {
			symbol: { type: 'string', description: 'The code symbol to query.' },
		},
		required: ['symbol'],
		needApprove: false
	},
	[ToolNameEnum.CLANGD_QUERY]: {
		name: ToolNameEnum.CLANGD_QUERY,
		description: `Use the clangd tool to output the symbol references.`,
		params: {
			filePath: { type: 'string', description: 'The file path to query.' },
			line: { type: 'number', description: 'The symbol line number.' },
			character: { type: 'number', description: 'The symbol character number.' },
		},
		required: ['filePath', 'line', 'character'],
		needApprove: false
	},
	[ToolNameEnum.SHOW_SUMMARY]: {
		name: ToolNameEnum.SHOW_SUMMARY,
		description: `Using folding tags to display detailed information and summaries to users.`,
		params: {
			summary: { type: 'string', description: 'The summary of the information.' },
			detail: { type: 'string', description: 'The detail of the information.' },
		},
		required: ['summary', 'detail'],
		needApprove: false
	},
	[ToolNameEnum.EXEC_COMMAND]: {
		name: ToolNameEnum.EXEC_COMMAND,
		description: `Execute a command on the terminal and return the execution result.`,
		params: {
			workdir: { type: 'string', description: 'Path to execute command.' },
			command: { type: 'string', description: 'Executed commands.' },
		},
		required: ['command'],
		needApprove: true
	},
} satisfies { [key in ToolNameEnum]: InternalToolInfo };

export type ToolName = ToolNameEnum;

export const toolNamesSet = new Set<string>(Object.values(ToolNameEnum));
export const isAToolName = (toolName: string): toolName is ToolName => {
	const isAToolName = toolNamesSet.has(toolName);
	return isAToolName;
};


export type ToolParamNames<T extends ToolName> = keyof typeof codeseekTools[T]['params'];
export type ToolParamsObj<T extends ToolName> = { [paramName in ToolParamNames<T>]: unknown };


export type ToolCallParamsType = {
	[ToolNameEnum.READ_FILE]: { path: string };
	[ToolNameEnum.LIST_FILES]: { path: string };
	[ToolNameEnum.PATHNAME_SEARCH]: { query: string };
	[ToolNameEnum.SEARCH]: { query: string };
	[ToolNameEnum.CREATE_FILE]: { path: string };
	[ToolNameEnum.UPDATE_TO_FILE]: { path: string; content: string; start: number; end: number };
	[ToolNameEnum.APPROVE_REQUEST]: { content: string; command: string };
	[ToolNameEnum.ASK_FOLLOWUP_QUESTION]: { question: string };
	[ToolNameEnum.CTAGS_QUERY]: { symbol: string };
	[ToolNameEnum.CLANGD_QUERY]: { filePath: string, line: number, character: number };
	[ToolNameEnum.SHOW_SUMMARY]: { summary: string, detail: string };
	[ToolNameEnum.EXEC_COMMAND]: { workdir?: string, command: string };
};


export type ReadFileResultType = { uri: URI; fileContents: string; hasNextPage: boolean; startLine: number; endLine: number };
export type ListFilesResultType = { rootURI: URI; children: DirectoryItem[] | null; hasNextPage: boolean; hasPrevPage: boolean; itemsRemaining: number };
export type PathnameSearchResultType = { queryStr: string; uris: URI[] | string; hasNextPage: boolean };
export type SearchResultType = { queryStr: string; uris: URI[] | string; hasNextPage: boolean };
export type CreateFileResultType = {};
export type UpdateToFileResultType = { content: string; uri: URI };
export type ApproveRequestResultType = { content: string; response: AskReponseType.yesButtonClicked | AskReponseType.noButtonClicked };
export type AskFollowupQuestionResultType = { content: string };
export type CommandResultType = { output: string };
export type CtagsQueryResultType = {
	rawLineContent: string;
	name: string;
	path: string;
	scopePath: string;
	line: number;
	kind: string;
	language: string;
	positions?: [number, number];
}[];
export type ClangdQueryResultType = {
	uri: string;
	range: {
		start: { line: number; character: number };
		end: { line: number; character: number };
	};
}[];

export type ToolCallReturnType = {
	[ToolNameEnum.READ_FILE]: ReadFileResultType;
	[ToolNameEnum.LIST_FILES]: ListFilesResultType;
	[ToolNameEnum.PATHNAME_SEARCH]: PathnameSearchResultType;
	[ToolNameEnum.SEARCH]: SearchResultType;
	[ToolNameEnum.CREATE_FILE]: CreateFileResultType;
	[ToolNameEnum.UPDATE_TO_FILE]: UpdateToFileResultType;
	[ToolNameEnum.APPROVE_REQUEST]: ApproveRequestResultType;
	[ToolNameEnum.ASK_FOLLOWUP_QUESTION]: AskFollowupQuestionResultType;
	[ToolNameEnum.CTAGS_QUERY]: CtagsQueryResultType;
	[ToolNameEnum.CLANGD_QUERY]: ClangdQueryResultType;
	[ToolNameEnum.SHOW_SUMMARY]: void;
	[ToolNameEnum.EXEC_COMMAND]: CommandResultType;
};

export type DirectoryItem = {
	name: string;
	isDirectory: boolean;
	isSymbolicLink: boolean;
};

// export type ToolNeedApprove = { [T in ToolName & keyof TooLNeedApproveBool]: TooLNeedApproveBool[T] };
export type ToolFns = { [T in ToolName]: (p: ToolCallParamsType[T], callback?: () => any, cancellationToken?: CancellationToken) => Promise<ToolCallReturnType[T]> };
export type ToolResultToString = { [T in ToolName]: (result: ToolCallReturnType[T]) => string };
