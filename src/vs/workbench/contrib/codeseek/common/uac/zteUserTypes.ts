

export enum ZteUserLoginChannelCommand {
	UDS_LOGIN = "udsLogin",
	CODE_SERVER_LOGIN = "codeServerPwdFree",
	GET_USER_INFO = "getUserInfo",
	LOGOUT = "logout",
	QR_LOGIN = "qrLogin",
	LOGIN_WITH_PWD = "loginWithPwd",
	PROMBLES_CHECK = "PROMBLES_CHECK",

	ON_DID_CHANGE_USERINFO = "onDidChangeUserInfo",
	ON_DID_USER_LOGIN = "onDidUserLogin",
	ON_DID_USER_LOGOUT = "onDidUserLogout",
	ON_OPEN_UAC_LOGIN_PAGE = "onOpenUacLoginPage",
}

export const ZTE_USER_LOGIN_CHANNEL_NAME = "uac-ipc-channel"
