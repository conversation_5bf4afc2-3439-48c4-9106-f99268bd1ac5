import { io, Socket } from 'socket.io-client';
import {
	ListFilesResultType,
	PathnameSearchResultType,
	ReadFileResultType,
	SearchResultType,
	ToolCallParamsType,
	ToolName,
	ToolNameEnum,
	CtagsQueryResultType,
	ClangdQueryResultType,
} from '../../common/toolsServiceTypes.js';
import { LLMMessageChannel } from '../channel/llmMessageChannel.js';
import { LLMChatMessage, ToolCallResultCode, ToolCallResultType, ToolCallType } from '../../common/llmMessageTypes.js';
import { ModelProvider } from './llmProxy.js';
import os from "os";
import osName from "os-name";
import { getShell } from '../utils/shell.js';
import { AgentParamsFromIde, EnvironmentDetails, SystemInformation } from '../../browser/codeseekAgentTaskService.js';
import { env, IdeTestConfig } from '../utils/common.js';
import { ICodeseekLogger } from '../../common/codeseekLogService.js';

export enum SocketEvent {
	connect = 'connect',
	oh_action = 'oh_action',
	oh_event = 'oh_event',
}

export type ReportMessage = {
	task_id: string;
	content: string;
	data: {
		task: Record<string, any>;
		ide: IdeData;
	};
};

export type IdeData = AgentParamsFromIde & {
	provider: {
		name: string;
		base_url: string;
		model: string;
		api_key: string;
	};
};

export type EventReceivedMessage = {
	code: ToolCallResultCode;
	task_id: string;
	topic: any;
	content: string;
	command: string;
	tool?: {
		name: ToolName;
		params: ToolCallParamsType[ToolName];
	};
};

export type AgentResult = {
	path?: string;
	content: string;
};

export type EventSendMessage = {
	code: ToolCallResultCode;
	task_id: string;
	topic: any;
	result: AgentResult | undefined;
	name: ToolName;
	msg: string | undefined;
};

export const endToken = '<end/>' as const;

export class AgentSocket {
	private socket: Socket | null = null;
	private eventQueue: Array<EventReceivedMessage> = [];
	private isProcessing = false;
	private toolCallResultDisposable: any = null;
	private fullText: string = '';
	private uri: string = 'https://10.89.239.77:28001';

	constructor(
		private requestId: string,
		private llmChannel: LLMMessageChannel | undefined,
		private onText: (data: { newText: string; fullText: string; newReasoning: string; fullReasoning: string }) => void,
		private onToolCall: (data: { fullText: string; toolCall: ToolCallType }) => void,
		private onFinalMessage: (data: { fullText: string }) => void,
		private onError: (error: any) => void,
		private logger: ICodeseekLogger
	) {
		if (env.AI_IDE_TEST_CONFIG) {
			const config: IdeTestConfig = JSON.parse(env.AI_IDE_TEST_CONFIG);
			if (config.agentSocketUri) {
				this.uri = config.agentSocketUri;
			}
		}
		this.connect();
	}

	private connect() {
		try {
			if (!this.uri) {
				this.logger.error('Agent socket uri is not set');
				this.onError({ message: 'Agent socket uri is not set', fullError: null });
				return;
			}
			this.logger.info(`Connecting to agent socket, uri: ${this.uri}`);
			this.socket = io(this.uri, {
				transports: ["websocket"],
				query: {
					task_id: this.requestId,
				},
				path: '/api/code-agent/v1/socket.io',
				reconnection: true,
				reconnectionAttempts: 3,
				reconnectionDelay: 1000,
				reconnectionDelayMax: 5000,
				timeout: 2000,
				rejectUnauthorized: false
			});
		} catch (error) {
			this.logger.error('Error connecting to agent socket', error);
			this.onError({ message: error.message, fullError: error });
		}
	}

	private provideReportMessage(messages: LLMChatMessage[], agentParamsFromIde: AgentParamsFromIde, modelProvider: ModelProvider, agentParamsFromPlugin: any): ReportMessage {
		if (!agentParamsFromIde.system_information.default_shell) {
			agentParamsFromIde.system_information.default_shell = getShell();
		}
		const system_information: SystemInformation = {
			"operating_system": osName(),
			"home_directory": os.homedir(),
			...agentParamsFromIde?.system_information,
		};

		const environment_details: EnvironmentDetails = {
			...agentParamsFromIde?.environment_details,
		};

		const ideData: IdeData = {
			...agentParamsFromIde!,
			provider: modelProvider,
			system_information: system_information,
			environment_details: environment_details,
		};

		return {
			task_id: this.requestId,
			content: messages[0].content,
			data: {
				task: agentParamsFromPlugin ?? {},
				ide: ideData
			},
		};
	}

	public agentLoop(messages: LLMChatMessage[], agentParamsFromIde: AgentParamsFromIde, modelProvider: ModelProvider, agentParamsFromPlugin: any) {
		if (!this.socket) return;
		const reportMessage = this.provideReportMessage(messages, agentParamsFromIde, modelProvider, agentParamsFromPlugin);

		this.socket.on(SocketEvent.connect, () => {
			this.logger.info(`Connected to server, socket id: ${this.socket?.id}, task id: ${this.requestId}`);
			this.logger.info(`Sending report message: ${JSON.stringify(reportMessage)}`);
			this.socket?.emit(SocketEvent.oh_action, reportMessage);
			this.socket?.on(SocketEvent.oh_event, (data: any) => {
				try {
					const data_ = data satisfies EventReceivedMessage;
					this.logger.info(`Received event: ${JSON.stringify(data_)}`);
					if (data_.task_id === this.requestId) {
						this.eventQueue.push(data_);

						if (!this.isProcessing) {
							this.processNextEvent();
						}
					}
				} catch (error) {
					this.logger.error(`Error processing event: ${JSON.stringify(error)}`);
					this.onError({ message: error.message, fullError: error });
				}
			});
		});
	}

	private processNextEvent() {
		if (this.isProcessing || this.eventQueue.length === 0) return;

		this.isProcessing = true;
		const data: EventReceivedMessage = this.eventQueue.shift()!;

		if (data.content === endToken) {
			this.onFinalMessage({ fullText: this.fullText });
			if (this.socket && this.socket.connected) {
				this.socket.disconnect();
			}
			if (this.toolCallResultDisposable && typeof this.toolCallResultDisposable.dispose === 'function') {
				this.toolCallResultDisposable.dispose();
			}
			this.isProcessing = false;
			this.eventQueue.length = 0;
			return;
		}

		if (data.content && data.content !== endToken) {
			this.handleText(data.content);
			if (!data.tool || !data.tool.name) {
				this.isProcessing = false;
				this.processNextEvent();
			}
		}
		if (data.tool && data.tool.name) {
			this.handleToolCall(data);
		}
		this.isProcessing = false;
		this.processNextEvent();
	}

	private handleText(text: string) {
		if (this.fullText) {
			this.fullText += '\n\n';
		}

		let processedLength = 0;
		const streamInterval = 10;
		const chunkSize = 3;

		while (processedLength < text.length) {
			const endIndex = Math.min(processedLength + chunkSize, text.length);
			const newChunk = text.substring(processedLength, endIndex);
			processedLength = endIndex;
			this.fullText += newChunk;

			this.onText({
				newText: newChunk,
				fullText: this.fullText,
				newReasoning: '',
				fullReasoning: '',
			});
			setTimeout(() => { }, streamInterval);
		}
	}

	private handleToolCall(data: EventReceivedMessage) {
		if (!data.tool || !data.tool.name) return;
		this.onToolCall({
			fullText: this.fullText,
			toolCall: {
				name: data.tool.name,
				command: data.command,
				params: data.tool.params,
				id: data.task_id
			}
		});
		this.fullText = '';
		if (this.llmChannel && typeof this.llmChannel.onToolCallResult === 'function') {
			if (this.toolCallResultDisposable && typeof this.toolCallResultDisposable.dispose === 'function') {
				this.toolCallResultDisposable.dispose();
			}
			this.toolCallResultDisposable = this.llmChannel.onToolCallResult(({ requestId, toolCallResult }: { requestId: string; toolCallResult: ToolCallResultType }) => {
				if (requestId === this.requestId && toolCallResult) {
					const message = this.getEventSendMessage(data, toolCallResult);
					if (message && this.socket && this.socket.connected) {
						this.logger.info(`Sending tool call result message: ${JSON.stringify(message)}`);
						this.socket.emit(SocketEvent.oh_action, message);
					}
					if (this.toolCallResultDisposable && typeof this.toolCallResultDisposable.dispose === 'function') {
						this.toolCallResultDisposable.dispose();
						this.toolCallResultDisposable = null;
					}
				}
			});
		}
	}

	public disconnect() {
		if (this.socket && this.socket.connected) {
			this.logger.info(`Disconnecting from server, socket id: ${this.socket?.id}, task id: ${this.requestId}`);
			this.socket.disconnect();
			this.socket = null;
			this.eventQueue = [];
			this.isProcessing = false;
			this.toolCallResultDisposable = null;
			this.fullText = '';
		}
	}

	private getEventSendMessage(data: EventReceivedMessage, toolCallResult: ToolCallResultType): EventSendMessage {
		if (toolCallResult.code === ToolCallResultCode.failure) {
			this.onError({ message: toolCallResult.error, fullError: null });
			return {
				code: toolCallResult.code,
				task_id: this.requestId,
				topic: data.topic,
				result: undefined,
				name: toolCallResult.name,
				msg: toolCallResult.error || 'unknown error'
			};
		} else {
			return {
				code: toolCallResult.code,
				task_id: this.requestId,
				topic: data.topic,
				result: this.handleToolCallResult(data.tool?.name!, toolCallResult),
				name: toolCallResult.name,
				msg: undefined,
			};
		}
	}

	private handleToolCallResult(toolName: ToolName, toolCallResult: ToolCallResultType): AgentResult | undefined {
		const agentResult = {
			content: toolCallResult.content
		};
		let toolCallReturnType;
		switch (toolName) {
			case ToolNameEnum.READ_FILE:
				toolCallReturnType = toolCallResult.result as ReadFileResultType;
				return {
					path: toolCallReturnType.uri.fsPath,
					...agentResult
				};
			case ToolNameEnum.LIST_FILES:
				toolCallReturnType = toolCallResult.result as ListFilesResultType;
				return {
					path: toolCallReturnType.rootURI.fsPath,
					...agentResult
				};
			case ToolNameEnum.PATHNAME_SEARCH:
				toolCallReturnType = toolCallResult.result as PathnameSearchResultType;
				return {
					path: toolCallReturnType.queryStr,
					...agentResult
				};
			case ToolNameEnum.SEARCH:
				toolCallReturnType = toolCallResult.result as SearchResultType;
				return {
					path: toolCallReturnType.queryStr,
					...agentResult
				};
			case ToolNameEnum.APPROVE_REQUEST:
			case ToolNameEnum.ASK_FOLLOWUP_QUESTION:
			case ToolNameEnum.CTAGS_QUERY:
				toolCallReturnType = toolCallResult.result as CtagsQueryResultType;
				return {
					...agentResult
				};
			case ToolNameEnum.CLANGD_QUERY:
				toolCallReturnType = toolCallResult.result as ClangdQueryResultType;
				return {
					...agentResult
				};
			default:
				return agentResult;
		}
	}
}
