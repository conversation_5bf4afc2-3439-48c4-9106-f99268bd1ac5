const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 获取 Git hooks 目录
const gitHooksDir = path.join(__dirname, '..', '.git', 'hooks');
const customHooksDir = path.join(__dirname, '..', 'git-hooks');

// 确保 Git hooks 目录存在
if (!fs.existsSync(gitHooksDir)) {
	console.error('Git hooks 目录不存在，可能不在 Git 仓库中');
	process.exit(1);
}

// 读取自定义 hooks 目录中的所有文件
const hooks = fs.readdirSync(customHooksDir);

// 为每个 hook 文件创建符号链接或复制到 .git/hooks 目录
hooks.forEach(hook => {
	const sourcePath = path.join(customHooksDir, hook);
	const targetPath = path.join(gitHooksDir, hook);

	// 如果目标文件已存在，先删除
	if (fs.existsSync(targetPath)) {
		fs.unlinkSync(targetPath);
	}

	// 复制 hook 文件
	fs.copyFileSync(sourcePath, targetPath);

	// 添加执行权限
	fs.chmodSync(targetPath, '755');

	console.log(`已安装 Git hook: ${hook}`);
});

console.log('所有 Git hooks 安装完成！');
