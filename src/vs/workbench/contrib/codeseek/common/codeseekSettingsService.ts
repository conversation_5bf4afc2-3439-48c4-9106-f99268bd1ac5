/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { Emitter, Event } from '../../../../base/common/event.js';
import { Disposable } from '../../../../base/common/lifecycle.js';
import { deepClone } from '../../../../base/common/objects.js';
import { URI } from '../../../../base/common/uri.js';
import { IConfigurationChangeEvent, IConfigurationService } from '../../../../platform/configuration/common/configuration.js';
import { IEncryptionService } from '../../../../platform/encryption/common/encryptionService.js';
import { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { IStorageService, StorageScope, StorageTarget } from '../../../../platform/storage/common/storage.js';
import { IWorkspaceContextService } from '../../../../platform/workspace/common/workspace.js';
import { IRestfulApiService } from '../browser/restfulApiService.js';
import { LlmInfoConfig } from '../electron-main/llmMessage/llmInfoConfig.js';
import {
	CodebaseSettingsOfRepo,
	CodeseekModelInfo,
	ContainerSettings,
	FeatureName,
	FeatureNames,
	GlobalSettingName,
	GlobalSettings,
	ModelOption,
	ModelSelectionOfFeature,
	ProviderNames,
	SettingName,
	SettingsOfCodebase,
	SettingsOfProvider,
	defaultGlobalSettings,
	defaultModelsOfProvider,
	defaultProviderSettings,
	defaultSettingsOfCodebase,
	defaultSettingsOfProvider,
	modelSelectionsEqual,
	providerNames
} from './codeseekSettingsTypes.js';
import { getWorkspaceUri } from './helpers/path.js';
import { CODESEEK_SETTINGS_STORAGE_KEY, THREAD_ABSTRACT_STORAGE_KEY, THREAD_MESSAGES_STORAGE_KEY } from './storageKeys.js';

export enum ChatMode {
	/**
	 * Chat mode where the AI interacts in a conversational manner.
	 */
	Ask = 'Ask',

	/**
	 * Agent mode where the AI acts as an autonomous agent performing tasks.
	 */
	Agent = 'Agent',
}

export type CodeseekSettingsState = {
	readonly settingsOfProvider: SettingsOfProvider; // optionsOfProvider
	readonly modelSelectionOfFeature: ModelSelectionOfFeature; // stateOfFeature
	readonly globalSettings: GlobalSettings;

	readonly _modelOptions: ModelOption[]; // computed based on the two above items
	readonly chatMode: ChatMode;
	readonly settingsOfCodebase: CodebaseSettingsOfRepo;
	readonly containerSettings?: Record<string, ContainerSettings>;
};

const _updatedModelsAfterDefaultModelsChange = (defaultModelNames: string[], options: { existingModels: CodeseekModelInfo[] }) => {
	const { existingModels } = options;

	const existingModelsMap: Record<string, CodeseekModelInfo> = {};
	for (const existingModel of existingModels) {
		existingModelsMap[existingModel.modelName] = existingModel;
	}

	const newDefaultModels = defaultModelNames.map((modelName, i) => ({
		modelName,
		isSupportConfig: true,
		isAutodetected: true,
		isApplyModel: false,
		isHidden: !!existingModelsMap[modelName]?.isHidden,
	}));

	return [
		...newDefaultModels, // swap out all the default models for the new default models
		...existingModels.filter(m => !m.isSupportConfig), // keep any non-default (custom) models
	];
};


const _validatedState = (state: Omit<CodeseekSettingsState, '_modelOptions'>) => {

	let newSettingsOfProvider = state.settingsOfProvider;
	// recompute _didFillInProviderSettings
	for (const providerName of providerNames) {
		const settingsAtProvider = newSettingsOfProvider[providerName];

		const didFillInProviderSettings = settingsAtProvider.isDefault || !!settingsAtProvider['apiKey'];

		if (didFillInProviderSettings === settingsAtProvider._didFillInProviderSettings) continue;
		newSettingsOfProvider = {
			...newSettingsOfProvider,
			[providerName]: {
				...settingsAtProvider,
				_didFillInProviderSettings: didFillInProviderSettings,
			},
		};
	}

	// update model options
	const newModelOptions: ModelOption[] = [];
	for (const providerName of providerNames) {
		const providerTitle = providerName; // displayInfoOfProviderName(providerName).title.toLowerCase() // looks better lowercase, best practice to not use raw providerName
		if (!newSettingsOfProvider[providerName]._didFillInProviderSettings) continue; // if disabled, don't display model options
		for (const { modelName, isHidden, isApplyModel } of newSettingsOfProvider[providerName].models) {
			if (isHidden) continue;
			newModelOptions.push({ name: `${modelName} (${providerTitle})`, selection: { providerName, modelName, isApplyModel } });
		}
	}

	// now that model options are updated, make sure the selection is valid
	// if the user-selected model is no longer in the list, update the selection for each feature that needs it to something relevant (the 0th model available, or null)
	let newModelSelectionOfFeature = state.modelSelectionOfFeature;
	for (const featureName of Object.values(FeatureNames)) {

		const modelSelectionAtFeature = newModelSelectionOfFeature[featureName as FeatureName];

		let selnIdx = modelSelectionAtFeature === null ? -1 : newModelOptions.findIndex(m => modelSelectionsEqual(m.selection, modelSelectionAtFeature));
		if (featureName === FeatureNames.Apply) {
			selnIdx = modelSelectionAtFeature === null ? -1 : newModelOptions.findIndex(m => modelSelectionsEqual(m.selection, modelSelectionAtFeature) && m.selection.isApplyModel);
		}

		if (selnIdx !== -1) continue;
		const newModelSelection = newModelOptions.length === 0 ? null : featureName === FeatureNames.Apply ? newModelOptions.find(m => m.selection.isApplyModel)?.selection : newModelOptions[0].selection;
		newModelSelectionOfFeature = {
			...newModelSelectionOfFeature,
			[featureName]: newModelSelection
		};
	}

	const newState = {
		...state,
		settingsOfProvider: newSettingsOfProvider,
		modelSelectionOfFeature: newModelSelectionOfFeature,
		_modelOptions: newModelOptions,
	} satisfies CodeseekSettingsState;

	return newState;
};



let currentRepoUri: URI | null = null;

const defaultState = () => {
	const d: CodeseekSettingsState = {
		settingsOfProvider: deepClone(defaultSettingsOfProvider),
		modelSelectionOfFeature: { 'Ctrl+L': null, 'Ctrl+K': null, 'Autocomplete': null, 'Apply': null },
		globalSettings: deepClone(defaultGlobalSettings),
		_modelOptions: [], // computed later
		chatMode: ChatMode.Ask,
		settingsOfCodebase: {
			[currentRepoUri?.fsPath || '']: defaultSettingsOfCodebase
		},
		containerSettings: {},
	};
	return d;
};

export interface ICodeseekSettingsService {
	readonly _serviceBrand: undefined;
	readonly state: CodeseekSettingsState; // in order to play nicely with react, you should immutably change state
	readonly waitForInitState: Promise<void>;

	onDidChangeState: Event<void>;
	onDidReSyncCodebase: Event<void>;
	onDidDeleteCodebase: Event<void>;
	onDidPauseCodebase: Event<void>;
	onDidResumeCodebase: Event<void>;
	onDidUpdateCodebase: Event<void>;
	onDidChangeCodebaseSwitchStatus: Event<void>;

	setSettingOfProvider(providerName: ProviderNames, settingName: SettingName, newVal: SettingsOfProvider[ProviderNames][SettingName]): Promise<void>;
	setModelSelectionOfFeature(featureName: FeatureName, newVal: ModelSelectionOfFeature[FeatureName], containerId?: string, options?: { doNotApplyEffects?: true }): Promise<void>;
	setChatMode(newVal: ChatMode, options?: { containerId?: string }): void;
	setGlobalSetting(settingName: GlobalSettingName, newVal: GlobalSettings[GlobalSettingName]): Promise<void>;
	setSettingsOfCodebase(currentRepoUri: URI, newVal: SettingsOfCodebase): Promise<void>;
	resetSettings(): void;

	setAutodetectedModels(providerName: ProviderNames, modelNames: string[], logging: object): void;
	toggleModelHidden(providerName: ProviderNames, modelName: string): void;
	addModel(providerName: ProviderNames, modelName: string): void;
	deleteModel(providerName: ProviderNames, modelName: string): boolean;
	reSyncCodebase(): void;
	deleteCodebase(): void;
	pauseCodebase(): void;
	resumeCodebase(): void;

	getVSCodeProxySettings(): string;

	getModelSelectionForContainer(featureName: FeatureName, containerId?: string): ModelSelectionOfFeature[FeatureName];
	getChatModeForContainer(containerId?: string): ChatMode;
	// 新增外部API调用方法
	fetchDefaultModels(): Promise<LlmInfoConfig[]>;
}


export const ICodeseekSettingsService = createDecorator<ICodeseekSettingsService>('CodeseekSettingsService');
class CodeseekSettingsService extends Disposable implements ICodeseekSettingsService {
	_serviceBrand: undefined;

	private readonly _onDidChangeState = new Emitter<void>();
	private readonly _onDidReSyncCodebase = new Emitter<void>();
	private readonly _onDidDeleteCodebase = new Emitter<void>();
	private readonly _onDidPauseCodebase = new Emitter<void>();
	private readonly _onDidResumeCodebase = new Emitter<void>();
	private readonly _onDidUpdateCodebase = new Emitter<void>();
	private readonly _onDidChangeCodebaseSwitchStatus = new Emitter<void>();

	private readonly proxy_key = 'http.proxy';
	readonly onDidChangeState: Event<void> = this._onDidChangeState.event; // this is primarily for use in react, so react can listen + update on state changes
	readonly onDidReSyncCodebase: Event<void> = this._onDidReSyncCodebase.event;
	readonly onDidDeleteCodebase: Event<void> = this._onDidDeleteCodebase.event;
	readonly onDidPauseCodebase: Event<void> = this._onDidPauseCodebase.event;
	readonly onDidResumeCodebase: Event<void> = this._onDidResumeCodebase.event;
	readonly onDidUpdateCodebase: Event<void> = this._onDidUpdateCodebase.event;
	readonly onDidChangeCodebaseSwitchStatus: Event<void> = this._onDidChangeCodebaseSwitchStatus.event;

	state: CodeseekSettingsState;
	waitForInitState: Promise<void>; // await this if you need a valid state initially

	constructor(
		@IStorageService private readonly _storageService: IStorageService,
		@IEncryptionService private readonly _encryptionService: IEncryptionService,
		// @IMetricsService private readonly _metricsService: IMetricsService,
		@IConfigurationService private readonly _configurationService: IConfigurationService,
		@IWorkspaceContextService private readonly _workspaceContextService: IWorkspaceContextService,
		@IRestfulApiService private readonly _restfulApiService: IRestfulApiService,
		// could have used this, but it's clearer the way it is (+ slightly different eg StorageTarget.USER)
		// @ISecretStorageService private readonly _secretStorageService: ISecretStorageService,
	) {
		super();

		currentRepoUri = getWorkspaceUri(this._workspaceContextService).workspaceUri;
		// at the start, we haven't read the partial config yet, but we need to set state to something
		this.state = defaultState();
		this.state.globalSettings.httpProxy = this.getVSCodeProxySettings();
		this._register(this._configurationService.onDidChangeConfiguration(e => this._onConfigurationChanged(e)));
		this.state = _validatedState(this.state);

		let resolver: () => void = () => { };
		this.waitForInitState = new Promise((res, rej) => resolver = res);

		// read and update the actual state immediately
		this._readState().then(async readS => {
			// 首先尝试获取动态模型列表
			let dynamicProviderSettings = defaultSettingsOfProvider;
			try {
				// 为 default provider 动态获取模型列表
				const currentDefaultProvider = this.state.settingsOfProvider[ProviderNames.default];
				const staticDefaultProvider = defaultProviderSettings[ProviderNames.default];
				const baseURL = (currentDefaultProvider as any)?.baseURL || staticDefaultProvider.baseURL;

				if (baseURL) {
					const fetchedModels = await this.fetchDefaultModels();
					if (fetchedModels && fetchedModels.length > 0) {
						// 更新 defaultModelsOfProvider 中的默认模型列表
						const updatedDefaultModels = {
							...defaultModelsOfProvider,
							[ProviderNames.default]: {
								models: fetchedModels.map(model => model.modelName),
								applyModels: (fetchedModels.filter(model =>
									model.supportsApply
								).length > 0
									? fetchedModels.filter(model =>
										model.supportsApply
									)
									: [fetchedModels[0]] // 如果没有apply模型，使用第一个模型
								).map(model => model.modelName)
							}
						};

						// 重新创建 defaultSettingsOfProvider
						dynamicProviderSettings = {
							...defaultSettingsOfProvider,
							[ProviderNames.default]: {
								...defaultSettingsOfProvider[ProviderNames.default],
								models: updatedDefaultModels[ProviderNames.default].models.map((modelName) => {
									const isApplyModel = updatedDefaultModels[ProviderNames.default].applyModels.includes(modelName);
									return {
										modelName,
										isSupportConfig: true,
										isAutodetected: false,
										isApplyModel: isApplyModel,
										isHidden: updatedDefaultModels[ProviderNames.default].models.length >= 10, // hide all models if there are a ton of them
									};
								})
							}
						};
					}
				}
			} catch (error) {
				console.warn('Failed to fetch dynamic models during initialization, using static defaults:', error);
			}

			// the stored data structure might be outdated, so we need to update it here (can do a more general solution later when we need to)
			const newSettingsOfProvider = {
				// A HACK BECAUSE WE ADDED DEEPSEEK (did not exist before, comes before readS)
				...(('deepseek' in dynamicProviderSettings) ? { deepseek: dynamicProviderSettings.deepseek } : {}),

				// A HACK BECAUSE WE ADDED XAI (did not exist before, comes before readS)
				...(('xAI' in dynamicProviderSettings) ? { xAI: dynamicProviderSettings.xAI } : {}),

				// A HACK BECAUSE WE ADDED VLLM (did not exist before, comes before readS)
				...(('vLLM' in dynamicProviderSettings) ? { vLLM: dynamicProviderSettings.vLLM } : {}),

				...readS.settingsOfProvider,

				// A HACK BECAUSE WE ADDED NEW GEMINI MODELS (existed before, comes after readS)
				...(('gemini' in dynamicProviderSettings) ? {
					gemini: {
						...((readS.settingsOfProvider as any).gemini ?? {}),
						models: [
							...((readS.settingsOfProvider as any).gemini?.models ?? []),
							...(((dynamicProviderSettings as any).gemini?.models ?? []).filter((m: any) =>
								!(readS.settingsOfProvider as any).gemini?.models?.find((m2: any) => m2.modelName === m.modelName)
							))
						]
					}
				} : {}),
				...(('saturn' in dynamicProviderSettings) ? {
					saturn: {
						...(dynamicProviderSettings.saturn as any),
						models: [
							...(dynamicProviderSettings.saturn as any).models,
						]
					}
				} : {}),
				...(('default' in dynamicProviderSettings) ? {
					default: {
						...dynamicProviderSettings.default,
						models: [
							...dynamicProviderSettings.default.models,
						]
					}
				} : {}),
			};
			const newModelSelectionOfFeature = {
				// A HACK BECAUSE WE ADDED FastApply
				...{ 'Apply': null },
				...readS.modelSelectionOfFeature,
			};

			const newGlobalSettings = {
				...this.state.globalSettings,
				...readS.globalSettings,
			};

			const newSettingsOfCodebase = {
				...this.state.settingsOfCodebase,
				...readS.settingsOfCodebase,
			};

			readS = {
				...this.state,
				...readS,
				settingsOfProvider: newSettingsOfProvider,
				modelSelectionOfFeature: newModelSelectionOfFeature,
				globalSettings: newGlobalSettings,
				settingsOfCodebase: newSettingsOfCodebase,
			};
			this.state = _validatedState(readS);

			resolver();
			this._onDidChangeState.fire();
		});

		this.fireOnDidUpdateCodebase();
	}

	private _onConfigurationChanged(e: IConfigurationChangeEvent): void {
		if (e.affectsConfiguration(this.proxy_key)) {
			const newProxyValue = this.getVSCodeProxySettings();
			if (this.state.globalSettings.httpProxy !== newProxyValue) {
				this.setGlobalSetting('httpProxy', newProxyValue);
				this._storeState();
				this._onDidChangeState.fire();
			}
		}
	}

	public resetSettings() {
		this._storageService.remove(CODESEEK_SETTINGS_STORAGE_KEY, StorageScope.APPLICATION);
		this._storageService.remove(THREAD_ABSTRACT_STORAGE_KEY, StorageScope.WORKSPACE);
		this._storageService.remove(THREAD_MESSAGES_STORAGE_KEY, StorageScope.WORKSPACE);
		this.state = defaultState();
		this._onDidChangeState.fire();
	}

	private async _readState(): Promise<CodeseekSettingsState> {
		const encryptedState = this._storageService.get(CODESEEK_SETTINGS_STORAGE_KEY, StorageScope.APPLICATION);
		if (!encryptedState)
			return defaultState();

		const stateStr = await this._encryptionService.decrypt(encryptedState);
		return JSON.parse(stateStr);
	}


	private async _storeState() {
		const state = this.state;
		const encryptedState = await this._encryptionService.encrypt(JSON.stringify(state));
		this._storageService.store(CODESEEK_SETTINGS_STORAGE_KEY, encryptedState, StorageScope.APPLICATION, StorageTarget.USER);
	}

	async setSettingOfProvider(providerName: ProviderNames, settingName: SettingName, newVal: SettingsOfProvider[ProviderNames][SettingName]) {

		const newModelSelectionOfFeature = this.state.modelSelectionOfFeature;

		const newSettingsOfProvider: SettingsOfProvider = {
			...this.state.settingsOfProvider,
			[providerName]: {
				...this.state.settingsOfProvider[providerName],
				[settingName]: newVal,
			}
		};

		const newGlobalSettings = this.state.globalSettings;

		const newChatMode = this.state.chatMode;

		const newSettingsOfCodebase = this.state.settingsOfCodebase;

		const newContainerSettings = this.state.containerSettings;

		const newState = {
			modelSelectionOfFeature: newModelSelectionOfFeature,
			settingsOfProvider: newSettingsOfProvider,
			globalSettings: newGlobalSettings,
			chatMode: newChatMode,
			settingsOfCodebase: newSettingsOfCodebase,
			containerSettings: newContainerSettings,
		};

		this.state = _validatedState(newState);

		await this._storeState();
		this._onDidChangeState.fire();

	};


	async setGlobalSetting(settingName: GlobalSettingName, newVal: GlobalSettings[GlobalSettingName]) {
		const newState: CodeseekSettingsState = {
			...this.state,
			globalSettings: {
				...this.state.globalSettings,
				[settingName]: newVal
			}
		};
		this.state = newState;
		await this._storeState();
		this._onDidChangeState.fire();
		if (settingName === 'enableCodeBase') {
			this._onDidChangeCodebaseSwitchStatus.fire();
		}
	};

	async setSettingsOfCodebase(currentRepoUri: URI, newVal: SettingsOfCodebase) {
		const newState: CodeseekSettingsState = {
			...this.state,
			settingsOfCodebase: {
				...this.state.settingsOfCodebase,
				[currentRepoUri.fsPath]: this.state.settingsOfCodebase[currentRepoUri.fsPath] ? {
					...this.state.settingsOfCodebase[currentRepoUri.fsPath],
					...newVal
				} : newVal
			}
		};
		this.state = newState;
		await this._storeState();
		this._onDidChangeState.fire();

		this.fireOnDidUpdateCodebase();
	};

	private fireOnDidUpdateCodebase() {
		const status = this.state.settingsOfCodebase[currentRepoUri?.fsPath || '']?.Remote.status;
		if (status === 'completed') {
			if (this.state.globalSettings.enableCodeBase) {
				this._onDidUpdateCodebase.fire();
			}
		}
	}


	async setModelSelectionOfFeature(featureName: FeatureName, newVal: ModelSelectionOfFeature[FeatureName], containerId?: string, options?: { doNotApplyEffects?: true }) {
		let newState: CodeseekSettingsState;

		if (containerId) {
			const containerSettings = this.state.containerSettings || {};
			const settings = containerSettings[containerId] || {};
			const modelSelections = settings.modelSelections || { ...this.state.modelSelectionOfFeature };

			newState = {
				...this.state,
				containerSettings: {
					...containerSettings,
					[containerId]: {
						...settings,
						modelSelections: {
							...modelSelections,
							[featureName]: newVal
						}
					}
				}
			};
		} else {
			newState = {
				...this.state,
				modelSelectionOfFeature: {
					...this.state.modelSelectionOfFeature,
					[featureName]: newVal
				}
			};
		}

		this.state = newState;

		if (options?.doNotApplyEffects)
			return;

		await this._storeState();
		this._onDidChangeState.fire();
	};


	setChatMode(newVal: ChatMode, options?: { containerId?: string }) {
		if (options?.containerId) {
			const containerSettings = this.state.containerSettings || {};
			const settings = containerSettings[options.containerId] || {};

			this.state = {
				...this.state,
				containerSettings: {
					...containerSettings,
					[options.containerId]: {
						...settings,
						chatMode: newVal
					}
				}
			};
		} else {
			this.state = {
				...this.state,
				chatMode: newVal,
			};
		}
		this._onDidChangeState.fire();
	};

	setAutodetectedModels(providerName: ProviderNames, autodetectedModelNames: string[], logging: object) {

		const { models } = this.state.settingsOfProvider[providerName];
		const oldModelNames = models.map(m => m.modelName);

		const newModels = _updatedModelsAfterDefaultModelsChange(autodetectedModelNames, { existingModels: models });
		this.setSettingOfProvider(providerName, 'models', newModels);

		// if the models changed, log it
		const new_names = newModels.map(m => m.modelName);
		if (!(oldModelNames.length === new_names.length
			&& oldModelNames.every((_, i) => oldModelNames[i] === new_names[i]))
		) {
			// this._metricsService.capture('Autodetect Models', { providerName, newModels: newModels, ...logging });
		}
	}

	toggleModelHidden(providerName: ProviderNames, modelName: string) {
		const { models } = this.state.settingsOfProvider[providerName];
		const modelIdx = models.findIndex(m => m.modelName === modelName);
		if (modelIdx === -1) return;
		const newIsHidden = !models[modelIdx].isHidden;
		const newModels: CodeseekModelInfo[] = [
			...models.slice(0, modelIdx),
			{ ...models[modelIdx], isHidden: newIsHidden },
			...models.slice(modelIdx + 1, Infinity)
		];
		this.setSettingOfProvider(providerName, 'models', newModels);


	}

	addModel(providerName: ProviderNames, modelName: string) {
		const { models } = this.state.settingsOfProvider[providerName];
		const existingIdx = models.findIndex(m => m.modelName === modelName);
		if (existingIdx !== -1) return; // if exists, do nothing
		const newModels = [
			...models,
			{ modelName, isSupportConfig: false, isHidden: false, isApplyModel: false }
		];
		this.setSettingOfProvider(providerName, 'models', newModels);

		// this._metricsService.capture('Add Model', { providerName, modelName });

	}

	deleteModel(providerName: ProviderNames, modelName: string): boolean {
		const { models } = this.state.settingsOfProvider[providerName];
		const delIdx = models.findIndex(m => m.modelName === modelName);
		if (delIdx === -1) return false;
		const newModels = [
			...models.slice(0, delIdx), // delete the idx
			...models.slice(delIdx + 1, Infinity)
		];
		this.setSettingOfProvider(providerName, 'models', newModels);

		// this._metricsService.capture('Delete Model', { providerName, modelName });

		return true;
	}

	getVSCodeProxySettings(): string {
		return this._configurationService.getValue<string>(this.proxy_key);
	}

	reSyncCodebase() {
		this._onDidReSyncCodebase.fire();
	}

	deleteCodebase() {
		this._onDidDeleteCodebase.fire();
	}

	pauseCodebase() {
		this._onDidPauseCodebase.fire();
	}

	resumeCodebase() {
		this._onDidResumeCodebase.fire();
	}

	getModelSelectionForContainer(featureName: FeatureName, containerId?: string): ModelSelectionOfFeature[FeatureName] {
		if (containerId && this.state.containerSettings?.[containerId]?.modelSelections?.[featureName] !== undefined) {
			return this.state.containerSettings[containerId].modelSelections![featureName];
		}
		return this.state.modelSelectionOfFeature[featureName];
	}

	getChatModeForContainer(containerId?: string): ChatMode {
		if (containerId && this.state.containerSettings?.[containerId]?.chatMode !== undefined) {
			return this.state.containerSettings[containerId].chatMode!;
		}
		return this.state.chatMode;
	}

	/**
	 * 从外部API获取模型列表
	 * @returns 模型名称数组
	 */
	async fetchDefaultModels(): Promise<LlmInfoConfig[]> {
		try {
			return await this._restfulApiService.getAllModels();
		} catch (error) {
			console.error('获取外部模型列表失败:', error);
			throw new Error(`获取外部模型列表失败: ${error}`);
		}
	}
}


registerSingleton(ICodeseekSettingsService, CodeseekSettingsService, InstantiationType.Eager);
