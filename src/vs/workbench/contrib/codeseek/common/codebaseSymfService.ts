// import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
// import { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';
// import { SymfSearchResult } from '../electron-main/codebase/symfRunner.js';
// import { IMainProcessService } from '../../../../platform/ipc/common/mainProcessService.js';
// import { IChannel } from '../../../../base/parts/ipc/common/ipc.js';
// import { Disposable } from '../../../../base/common/lifecycle.js';
// import { IWorkspaceContextService } from '../../../../platform/workspace/common/workspace.js';
// import { DeleteIndexParams, EnsureIndexParams, GetIndexStatusParams, GetLiveResultsParams, searchParams, ICodebaseService, ISearchResult, ReindexIfStaleParams, GetResultsParams } from './codebaseTypes.js';
// import { ICodeseekSettingsService } from './codeseekSettingsService.js';
// import { ICodeseekFileService } from './codeseekFileService.js';
// import { ILLMMessageService } from './llmMessageService.js';
// import { ICodeseekLogger } from './codeseekLogService.js';
// import { URI } from '../../../../base/common/uri.js';
// import { FeatureNames } from './codeseekSettingsTypes.js';


// export const ICodebaseSymfService = createDecorator<ICodebaseService>('codeseekSymfService');

// export class CodebaseSymfService extends Disposable implements ICodebaseService {
// 	readonly _serviceBrand: undefined;
// 	private readonly channel: IChannel;
// 	private indexQueue: URI[] = [];
// 	private isIndexing = false;

// 	constructor(
// 		@IMainProcessService private readonly _mainProcessService: IMainProcessService,
// 		@IWorkspaceContextService private _workspaceContextService: IWorkspaceContextService,
// 		@ICodeseekSettingsService private readonly _codeseekSettingsService: ICodeseekSettingsService,
// 		@ICodeseekFileService private readonly codeseekFileService: ICodeseekFileService,
// 		@ILLMMessageService private readonly llmMessageService: ILLMMessageService,
// 		@ICodeseekLogger private readonly logService: ICodeseekLogger,
// 	) {
// 		super();

// 		this.channel = this._mainProcessService.getChannel('codeseek-codebase-channel');
// 		this.initialize();

// 		this._register(this._workspaceContextService.onDidChangeWorkspaceFolders(() => {
// 			this.initialize();
// 		}));
// 	}

// 	private async initialize(): Promise<void> {
// 		// if (true) {
// 		// 	this.logService.info('local codebase is not enabled, skip initialize');
// 		// 	return;
// 		// }
// 		// if (this._workspaceContextService.getWorkspace().folders.length > 0) {
// 		// 	// 清空之前的索引队列
// 		// 	this.indexQueue = [];

// 		// 	// 将所有需要索引的文件夹添加到队列
// 		// 	for (const folder of this._workspaceContextService.getWorkspace().folders) {
// 		// 		if (folder.uri.scheme === 'file') {
// 		// 			this.indexQueue.push(folder.uri);
// 		// 		}
// 		// 	}

// 		// 	// 启动异步索引处理
// 		// 	this.processIndexQueue();
// 		// }
// 	}

// 	private async processIndexQueue(): Promise<void> {
// 		if (this.isIndexing || this.indexQueue.length === 0) {
// 			return;
// 		}

// 		this.isIndexing = true;

// 		try {
// 			const uri = this.indexQueue.shift()!;
// 			this.logService.info(`[CodebaseSymfService] Starting index for: ${uri.toString()}`);

// 			await this.ensureIndex({
// 				repoUri: uri,
// 				options: {
// 					retryIfLastAttemptFailed: true,
// 					ignoreExisting: false,
// 				},
// 				onProgress: () => { }
// 			});
// 		} catch (error) {
// 			this.logService.error('[CodebaseSymfService] Error indexing:', error);
// 		} finally {
// 			this.isIndexing = false;
// 			// 继续处理队列中的下一个项目
// 			if (this.indexQueue.length > 0) {
// 				setTimeout(() => this.processIndexQueue(), 0);
// 			}
// 		}
// 	}

// 	public async search(params: searchParams): Promise<ISearchResult[]> {
// 		// if (!this._codeseekSettingsService.state.globalSettings.enableLocalCodeBase) {
// 		// 	this.logService.info('local codebase is not enabled, skip search');
// 		// 	return [];
// 		// }
// 		// // 检查索引状态，如果索引未完成则返回空结果
// 		// const indexStatus = await this.getIndexStatus({ repoUri: params.repoUri });
// 		// if (indexStatus !== 'ready') {
// 		// 	this.logService.info(`[CodebaseSymfService] Search skipped: index not ready, status: ${indexStatus}`);
// 		// 	return [];
// 		// }
// 		// const keywordQuery = await this.rewriteKeywordQuery(params.userQuery);

// 		// try {
// 		// 	const files = await this.codeseekFileService.getGitModifiedFiles();
// 		// 	const [liveResults, results] = await Promise.all([
// 		// 		this.getLiveResults({ userQuery: params.userQuery, keywordQuery, files: files.map(file => file.toString()) }),
// 		// 		this.getResults({ userQuery: params.userQuery, keywordQuery, repoUri: params.repoUri })
// 		// 	]);

// 		// 	const liveResultsMap = new Map(liveResults.map(result => [result.file, result]));
// 		// 	const mergedResults = [];
// 		// 	for (const result of results) {
// 		// 		const liveResult = liveResultsMap.get(result.file);
// 		// 		if (liveResult) {
// 		// 			mergedResults.push({ ...liveResult, ...result });
// 		// 		} else {
// 		// 			mergedResults.push(result);
// 		// 		}
// 		// 	}

// 		// 	const resultConvert = mergedResults.slice(0, params.topK).map(async result => {
// 		// 		const uri = URI.file(result.file.path);
// 		// 		const content = await this.codeseekFileService.readFile(uri, {
// 		// 			startLineNumber: result.range.startPoint.row + 1,
// 		// 			endLineNumber: result.range.endPoint.row + 1
// 		// 		});
// 		// 		const res: ISearchResult = {
// 		// 			uri,
// 		// 			range: {
// 		// 				startLineNumber: result.range.startPoint.row,
// 		// 				startColumn: result.range.startPoint.col,
// 		// 				endLineNumber: result.range.endPoint.row,
// 		// 				endColumn: result.range.endPoint.col
// 		// 			},
// 		// 			content: content
// 		// 		}
// 		// 		return res;
// 		// 	});
// 		// 	const finalResults = await Promise.all(resultConvert);
// 		// 	this.logService.info('[CodebaseSymfService] results: ', finalResults);
// 		// 	return finalResults;
// 		// } catch (error) {
// 		// 	this.logService.error('[CodebaseSymfService] Error in semantic search:', error);
// 		// 	return [];
// 		// }
// 		return [];
// 	}

// 	async getResults(params: GetResultsParams): Promise<SymfSearchResult[]> {
// 		return await this.channel.call('getSymfResults', params);
// 	}

// 	async getLiveResults(params: GetLiveResultsParams): Promise<SymfSearchResult[]> {
// 		if (params.files.length === 0) {
// 			return [];
// 		}
// 		return await this.channel.call('getSymfLiveResults', params);
// 	}

// 	async ensureIndex(params: EnsureIndexParams): Promise<void> {
// 		this.channel.call('ensureSymfIndex', params);
// 	}

// 	async deleteIndex(params: DeleteIndexParams): Promise<void> {
// 		this.channel.call('deleteSymfIndex', params);
// 	}

// 	async getIndexStatus(params: GetIndexStatusParams): Promise<'unindexed' | 'indexing' | 'ready' | 'failed'> {
// 		return await this.channel.call('getSymfIndexStatus', params);
// 	}

// 	async reindexIfStale(params: ReindexIfStaleParams): Promise<void> {
// 		this.channel.call('reSymfindexIfStale', params);
// 	}

// 	// 填充keywords
// 	private async rewriteKeywordQuery(query: string): Promise<string> {
// 		const message = `You are helping a developer answer questions about their codebase. Write a keyword search to help find the relevant files to answer the question. Examples:
//     - Find a symbol by name: \`<query>SearchJob</query>\`
//     - Find a symbol using keywords: \`<query>search indexing queue</query>\`
//     - Find where something is implemented: \`<query>check for authentication</query>\`
//     - Find string literal in code: \`<query>"result limit hit"</query>\`

//      ONLY return the keyword search. Question: <userQuery>${query}</userQuery>`;

// 		return new Promise<string>((resolve, reject) => {
// 			const timeoutId = setTimeout(() => {
// 				this.logService.warn('CodebaseSearch, rewriteKeywordQuery LLM request timed out');
// 				resolve(query);
// 			}, 10000);

// 			const requestId = this.llmMessageService.sendLLMMessage({
// 				useProviderFor: FeatureNames.CtrlK,
// 				messages: [{
// 					role: 'user',
// 					content: message
// 				}],
// 				messagesType: 'chatMessages',
// 				onText: () => { },
// 				onToolCall: () => { },
// 				onFinalMessage: (params) => {
// 					clearTimeout(timeoutId);

// 					let result = params.fullText.trim();

// 					const queryMatch = result.match(/<query>(.*?)<\/query>/);
// 					if (queryMatch && queryMatch[1]) {
// 						result = `<query>${queryMatch[1].trim()}</query>`
// 					}

// 					this.logService.info(`CodebaseSearch, rewriteKeywordQuery got result: ${result}`);
// 					resolve(result);
// 				},
// 				onError: (params) => {
// 					clearTimeout(timeoutId);
// 					this.logService.error('CodebaseSearch, rewriteKeywordQuery call LLM error:', params.message);
// 					resolve(query);
// 				},
// 				logging: { loggingName: 'CodebaseSearch' }
// 			});
// 			return requestId;
// 		});
// 	}
// }

// registerSingleton(ICodebaseSymfService, CodebaseSymfService, InstantiationType.Delayed);
