import { CancellationToken } from '../../../../../base/common/cancellation.js';
import { Event } from '../../../../../base/common/event.js';
import { IServerChannel } from '../../../../../base/parts/ipc/common/ipc.js';
import { ZteUserLoginChannelCommand } from '../../common/uac/zteUserTypes.js';
import { IUacLoginService } from './uacLoginService.js';


export class UacIpcChannel implements IServerChannel {
	constructor(private uacLoginService: IUacLoginService) { }
	call(ctx: string, command: string, arg?: any, cancellationToken?: CancellationToken): Promise<any> {
		switch (command) {
			case ZteUserLoginChannelCommand.UDS_LOGIN:
				return this.uacLoginService.udsLogin();
			case ZteUserLoginChannelCommand.CODE_SERVER_LOGIN:
				return this.uacLoginService.codeServerLogin(arg[0]);
			case ZteUserLoginChannelCommand.GET_USER_INFO:
				return this.uacLoginService.getUserInfo()
			case ZteUserLoginChannelCommand.LOGOUT:
				return this.uacLoginService.logout()
			case ZteUserLoginChannelCommand.LOGIN_WITH_PWD:
				return this.uacLoginService.login(arg[0])
			case ZteUserLoginChannelCommand.QR_LOGIN:
				return this.uacLoginService.qrLogin(arg[0], cancellationToken);
			case ZteUserLoginChannelCommand.PROMBLES_CHECK:
				return this.uacLoginService.problemCheck(arg[0], arg[1])
			default:
				throw new Error(`Unknown command: ${command}`);
		}
	}
	listen(ctx: string, event: string, arg?: any): Event<any> {
		switch (event) {
			case ZteUserLoginChannelCommand.ON_DID_CHANGE_USERINFO:
				return this.uacLoginService.onDidChangeUserInfo;
			case ZteUserLoginChannelCommand.ON_DID_USER_LOGIN:
				return this.uacLoginService.onDidUserLogin;
			case ZteUserLoginChannelCommand.ON_DID_USER_LOGOUT:
				return this.uacLoginService.onDidUserLogout;
			case ZteUserLoginChannelCommand.ON_OPEN_UAC_LOGIN_PAGE:
				return this.uacLoginService.onOpenUacLoginPage;
			default:
				throw new Error(`Unknown event: ${event}`);
		}
	}

}
