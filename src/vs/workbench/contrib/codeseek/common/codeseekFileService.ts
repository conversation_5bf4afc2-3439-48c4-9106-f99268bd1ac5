/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { VSBuffer } from '../../../../base/common/buffer.js';
import { isWindows } from '../../../../base/common/platform.js';
import { URI } from '../../../../base/common/uri.js';
import { EndOfLinePreference, ITextModel } from '../../../../editor/common/model.js';
import { IModelService } from '../../../../editor/common/services/model.js';
import { IFileService } from '../../../../platform/files/common/files.js';
import { registerSingleton, InstantiationType } from '../../../../platform/instantiation/common/extensions.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { IWorkspaceContextService } from '../../../../platform/workspace/common/workspace.js';
import { ISCMService } from '../../scm/common/scm.js';
import { ICodeseekLogger } from './codeseekLogService.js';
import { filenameToVscodeLanguage } from './helpers/detectLanguage.js';
import { getWorkspaceUri } from './helpers/path.js';

export const defaultExcludeFolders = [
	'node_modules', 'dist', 'build', 'target', 'out', 'logs', 'tmp', 'cache',
	'temp', 'tempfiles', 'venv'
];

// linebreak symbols
export const allLinebreakSymbols = ['\r\n', '\n'];
export const _ln = isWindows ? allLinebreakSymbols[0] : allLinebreakSymbols[1];

export interface IProjectStructure {
	root: IDirectoryNode | null;
}

export interface IDirectoryNode {
	name: string;
	uri: URI;
	type: 'directory';
	children: Array<IDirectoryNode | IFileNode>;
}

export interface IFileNode {
	name: string;
	uri: URI;
	type: 'file';
	language: string | undefined;
}

export enum FileType {
	Unknown = 0,
	File = 1,
	Directory = 2,
	SymbolicLink = 64
}

// 目录树构建任务队列项接口
interface IDirectoryTask {
	node: IDirectoryNode;
	depth: number;
}

// 目录树构建选项接口
interface IBuildOptions {
	maxDepth: number;
	excludeFolders: Set<string>;
	maxConcurrency: number;
	enableCache: boolean;
}

export interface ICodeseekFileService {
	readonly _serviceBrand: undefined;

	readFile(uri: URI, range?: { startLineNumber: number; endLineNumber: number }): Promise<string>;
	readModel(uri: URI, range?: { startLineNumber: number; endLineNumber: number }): string | null;
	getModel(uri: URI): ITextModel | null;
	updateFile(uri: URI, content: string, start: number, end: number): Promise<string>;
	getGitModifiedFiles(): Promise<URI[]>;
	getProjectStructure(maxDepth: number, excludeFolders?: string[]): Promise<IProjectStructure>;
	readDirectory(uri: URI): Promise<[string, FileType][]>;
	clearDirectoryCache(): void;
}

export const ICodeseekFileService = createDecorator<ICodeseekFileService>('CodeseekFileService');


// implemented by calling channel
export class CodeseekFileService implements ICodeseekFileService {
	readonly _serviceBrand: undefined;

	// 目录内容缓存
	private readonly directoryCache = new Map<string, [string, FileType][]>();
	// 缓存过期时间（5分钟）
	private readonly cacheExpiration = new Map<string, number>();
	private readonly CACHE_TTL = 5 * 60 * 1000; // 5分钟

	constructor(
		@IModelService private readonly modelService: IModelService,
		@IFileService private readonly fileService: IFileService,
		@ISCMService private readonly scmService: ISCMService,
		@ICodeseekLogger private readonly logService: ICodeseekLogger,
		@IWorkspaceContextService private readonly workspaceService: IWorkspaceContextService,
	) {

	}

	async readFile(uri: URI, range?: { startLineNumber: number; endLineNumber: number }): Promise<string> {
		const modelResult = this.readModel(uri, range);
		if (modelResult) return modelResult;

		// if no model, read the raw file
		const fileResult = await this._readFileRaw(uri, range);
		if (fileResult) return fileResult;

		return '';
	};

	async _readFileRaw(uri: URI, range?: { startLineNumber: number; endLineNumber: number }): Promise<string | null> {
		try {
			const res = await this.fileService.readFile(uri);
			if (range) {
				return res.value.toString()
					.split(_ln)
					.slice(range.startLineNumber - 1, range.endLineNumber)
					.join(_ln);
			}
			return res.value.toString();
		} catch (e) {
			return null;
		}
	};

	getModel(uri: URI): ITextModel | null {
		return this.modelService.getModel(uri);
	}

	readModel(uri: URI, range?: { startLineNumber: number; endLineNumber: number }): string | null {

		// read saved model (sometimes null if the user reloads application)
		let model = this.modelService.getModel(uri);

		// check all opened models for the same `fsPath`
		if (!model) {
			const models = this.modelService.getModels();
			for (const m of models) {
				if (m.uri.fsPath === uri.fsPath) {
					model = m;
					break;
				}
			}
		}

		// if still not found, return
		if (!model) { return null; }

		// if range, read it
		if (range) {
			return model.getValueInRange({
				startLineNumber: range.startLineNumber,
				endLineNumber: range.endLineNumber,
				startColumn: 1,
				endColumn: Number.MAX_VALUE
			}, EndOfLinePreference.LF);
		} else {
			return model.getValue(EndOfLinePreference.LF);
		}

	};

	async updateFile(uri: URI, content: string, start: number, end: number): Promise<string> {

		try { // this throws an error if no file exists (eg it was deleted)

			const res = await this.fileService.readFile(uri);
			const fileContent = res.value.toString().split(_ln);

			fileContent.splice(start - 1, end - start + 1, ...content.split(_ln));

			const newFileContent = fileContent.join(_ln);
			this.fileService.writeFile(uri, VSBuffer.fromString(newFileContent)).then(res => {
				return 'success';
			}).catch(e => {
				return 'failed';
			});
			return 'success';
		} catch (e) {
			return 'failed';
		}
	};

	async getGitModifiedFiles(): Promise<URI[]> {
		this.logService.info('Getting Git modified files');

		const modifiedFiles: URI[] = [];

		// 获取所有SCM资源组
		const repositories = this.scmService.repositories;

		for (const repository of repositories) {
			const resourceGroups = repository.provider.groups;

			for (const group of resourceGroups) {
				for (const resource of group.resources) {
					if (resource.sourceUri) {
						modifiedFiles.push(resource.sourceUri);
					}
				}
			}
		}

		return modifiedFiles;
	}

	async getProjectStructure(maxDepth: number = Infinity, excludeFolders: string[] = defaultExcludeFolders): Promise<IProjectStructure> {
		const { workspaceName, workspaceUri } = getWorkspaceUri(this.workspaceService);
		if (!workspaceUri) {
			return { root: null };
		}

		const root: IDirectoryNode = {
			name: workspaceName,
			uri: workspaceUri,
			type: 'directory',
			children: []
		};

		const options: IBuildOptions = {
			maxDepth,
			excludeFolders: new Set(excludeFolders),
			maxConcurrency: Math.min(10, Math.max(2, navigator.hardwareConcurrency || 4)),
			enableCache: true
		};

		await this.buildDirectoryTree(root, options);

		return { root };
	}

	/**
	 * 目录树构建方法，使用广度优先搜索 + 并发处理 + 缓存机制
	 */
	private async buildDirectoryTree(root: IDirectoryNode, options: IBuildOptions): Promise<void> {
		const { maxDepth, excludeFolders, maxConcurrency, enableCache } = options;

		// 使用队列实现广度优先遍历
		const taskQueue: IDirectoryTask[] = [{ node: root, depth: maxDepth }];

		while (taskQueue.length > 0) {
			// 取出当前层级的所有任务
			const currentLevelTasks: IDirectoryTask[] = [];
			const batchSize = Math.min(maxConcurrency, taskQueue.length);

			for (let i = 0; i < batchSize; i++) {
				const task = taskQueue.shift();
				if (task) {
					currentLevelTasks.push(task);
				}
			}

			// 并发处理当前层级的目录
			const results = await Promise.allSettled(
				currentLevelTasks.map(task => this.processDirectoryTask(task, excludeFolders, enableCache))
			);

			// 收集下一层级的任务
			for (let i = 0; i < results.length; i++) {
				const result = results[i];
				const task = currentLevelTasks[i];

				if (result.status === 'fulfilled' && task.depth > 1) {
					// 将子目录添加到队列中
					taskQueue.push(...result.value);
				} else if (result.status === 'rejected') {
					// 记录错误但不中断整个过程
					this.logService.warn(`Failed to process directory ${task.node.uri.toString()}: ${result.reason}`);
				}
			}
		}
	}

	/**
	 * 处理单个目录任务
	 */
	private async processDirectoryTask(
		task: IDirectoryTask,
		excludeFolders: Set<string>,
		enableCache: boolean
	): Promise<IDirectoryTask[]> {
		const { node, depth } = task;
		const nextLevelTasks: IDirectoryTask[] = [];

		try {
			const entries = await this.readDirectoryWithCache(node.uri, enableCache);

			const children: Array<IDirectoryNode | IFileNode> = [];
			children.length = 0;

			for (const [name, type] of entries) {
				if (type === FileType.Directory && (excludeFolders.has(name) || name.startsWith('.'))) {
					continue;
				}

				const childUri = URI.joinPath(node.uri, name);

				if (type === FileType.Directory) {
					const dirNode: IDirectoryNode = {
						name,
						uri: childUri,
						type: 'directory',
						children: []
					};
					children.push(dirNode);

					// 添加到下一层级任务队列
					if (depth > 1) {
						nextLevelTasks.push({ node: dirNode, depth: depth - 1 });
					}
				} else if (type === FileType.File) {
					const language = filenameToVscodeLanguage(childUri.fsPath);
					const fileNode: IFileNode = {
						name,
						uri: childUri,
						type: 'file',
						language
					};
					children.push(fileNode);
				}
			}

			node.children = children;

		} catch (error) {
			this.logService.error(`Error processing directory ${node.uri.toString()}:`, error);
			node.children = [];
		}

		return nextLevelTasks;
	}

	/**
	 * 带缓存的目录读取
	 */
	private async readDirectoryWithCache(uri: URI, enableCache: boolean): Promise<[string, FileType][]> {
		const cacheKey = uri.toString();

		if (enableCache) {
			const cached = this.directoryCache.get(cacheKey);
			const expiration = this.cacheExpiration.get(cacheKey);

			if (cached && expiration && Date.now() < expiration) {
				return cached;
			}
		}

		const result = await this.readDirectory(uri);

		if (enableCache) {
			this.directoryCache.set(cacheKey, result);
			this.cacheExpiration.set(cacheKey, Date.now() + this.CACHE_TTL);

			this.cleanExpiredCache();
		}

		return result;
	}

	/**
	 * 清理过期缓存
	 */
	private cleanExpiredCache(): void {
		const now = Date.now();
		const expiredKeys: string[] = [];

		for (const [key, expiration] of this.cacheExpiration.entries()) {
			if (now >= expiration) {
				expiredKeys.push(key);
			}
		}

		for (const key of expiredKeys) {
			this.directoryCache.delete(key);
			this.cacheExpiration.delete(key);
		}
	}

	/**
	 * 清理缓存的公共方法
	 */
	public clearDirectoryCache(): void {
		this.directoryCache.clear();
		this.cacheExpiration.clear();
	}

	async readDirectory(uri: URI): Promise<[string, FileType][]> {
		const result = await this.fileService.resolve(uri, { resolveMetadata: true });
		if (!result.isDirectory) {
			throw new Error(`Not a directory: ${uri.toString()}`);
		}

		return result.children?.map(entry => {
			const type = entry.isDirectory ? FileType.Directory : FileType.File;
			return [entry.name, type];
		}) || [];
	}
}

registerSingleton(ICodeseekFileService, CodeseekFileService, InstantiationType.Eager);
