
import { CancellationToken } from '../../../../../../base/common/cancellation.js';
import { IHeaders, IRequestOptions } from '../../../../../../base/parts/request/common/request.js';

import { asJson, asTextOrError, IRequestService } from '../../../../../../platform/request/common/request.js';

export async function request<T>(requestService: IRequestService,
	options: { url: string, headers?: IHeaders, type: string, body?: string, json?: boolean },
	callback?: (error: Error | null, response: string | null, body: T | null) => void
) {
	const _options: IRequestOptions = {
		type: options.type,
		url: options.url,
		data: options.body,
		headers: options.headers
	}

	requestService.request(_options, CancellationToken.None).then(context => {

		if (context.res.statusCode !== 200) {
			throw new Error('接口错误');
		}
		if (options.json) {
			asJson<T>(context).then(body => {
				callback?.(null, '', body)
			});

		} else {
			asTextOrError(context).then(str => {
				callback?.(null, str, null)
			})
		}
	}).catch(e => {
		callback?.(e, e.message, null)
	});
}
