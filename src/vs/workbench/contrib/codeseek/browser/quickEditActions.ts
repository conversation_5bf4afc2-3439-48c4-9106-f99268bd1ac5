/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { KeyCode, KeyMod } from '../../../../base/common/keyCodes.js';
import { Action2, registerAction2 } from '../../../../platform/actions/common/actions.js';
import { ServicesAccessor } from '../../../../platform/instantiation/common/instantiation.js';
import { KeybindingWeight } from '../../../../platform/keybinding/common/keybindingsRegistry.js';
import { ICodeEditorService } from '../../../../editor/browser/services/codeEditorService.js';
import { IEditCodeService } from './editCodeService.js';
import { CODESEEK_CTRL_K_ACTION_ID } from './actionIDs.js';
import { localize2 } from '../../../../nls.js';
import { IQuickEditStateService } from './quickEditStateService.js';
import { roundRangeToLines } from './helpers/util.js';
import { StagingSelectionItem } from '../common/selectedFileService.js';


export type QuickEditPropsType = {
	diffareaid: number;
	textAreaRef: (ref: HTMLTextAreaElement | null) => void;
	onChangeHeight: (height: number) => void;
	onChangeText: (text: string) => void;
	onClose: () => void;
	initText: string | null;
	selections: StagingSelectionItem[];
	setSelections: (selections: StagingSelectionItem[]) => void;
};

export type QuickEdit = {
	startLine: number; // 0-indexed
	beforeCode: string;
	afterCode?: string;
	instructions?: string;
	responseText?: string; // model can produce a text response too
};


registerAction2(class extends Action2 {
	constructor(
	) {
		super({
			id: CODESEEK_CTRL_K_ACTION_ID,
			f1: true,
			title: localize2('codeseekQuickEditAction', 'Codeseek: Quick Edit'),
			keybinding: {
				primary: KeyMod.CtrlCmd | KeyCode.KeyK,
				weight: KeybindingWeight.CodeseekExtension,
			}
		});
	}

	async run(accessor: ServicesAccessor): Promise<void> {
		const editorService = accessor.get(ICodeEditorService);
		const editCodeService = accessor.get(IEditCodeService);
		const quickEditStateService = accessor.get(IQuickEditStateService);

		editCodeService.disposeTip('TextSelectionTipZone');
		const editor = editorService.getActiveCodeEditor();
		if (!editor) return;
		const model = editor.getModel();
		if (!model) return;
		const selection = roundRangeToLines(editor.getSelection(), { emptySelectionBehavior: 'line' });
		if (!selection) return;
		const { startLineNumber: startLine, endLineNumber: endLine } = selection;
		editCodeService.addCtrlKZone({ startLine, endLine, editor });
		quickEditStateService.setState({ isOpenEdit: true });

	}
});
