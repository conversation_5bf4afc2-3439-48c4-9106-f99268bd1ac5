/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';
import { EditorInput } from '../../../common/editor/editorInput.js';
import * as nls from '../../../../nls.js';
import { EditorExtensions } from '../../../common/editor.js';
import { EditorPane } from '../../../browser/parts/editor/editorPane.js';
import { IEditorGroup, IEditorGroupsService } from '../../../services/editor/common/editorGroupsService.js';
import { ITelemetryService } from '../../../../platform/telemetry/common/telemetry.js';
import { IThemeService } from '../../../../platform/theme/common/themeService.js';
import { IStorageService } from '../../../../platform/storage/common/storage.js';
import { Dimension } from '../../../../base/browser/dom.js';
import { EditorPaneDescriptor, IEditorPaneRegistry } from '../../../browser/editor.js';
import { SyncDescriptor } from '../../../../platform/instantiation/common/descriptors.js';
import { Action2, MenuId, MenuRegistry, registerAction2 } from '../../../../platform/actions/common/actions.js';
import { Registry } from '../../../../platform/registry/common/platform.js';
import { ServicesAccessor } from '../../../../editor/browser/editorExtensions.js';
import { IEditorService } from '../../../services/editor/common/editorService.js';
import { URI } from '../../../../base/common/uri.js';
import { ContextKeyExpr } from '../../../../platform/contextkey/common/contextkey.js';


import { mountCodeseekSettings } from './react/out/codeseek-settings-tsx/index.js';
import { Codicon } from '../../../../base/common/codicons.js';
import { IDisposable } from '../../../../base/common/lifecycle.js';
import { ICodeseekSettingsService } from '../common/codeseekSettingsService.js';


// refer to preferences.contribution.ts keybindings editor

class CodeseekSettingsInput extends EditorInput {

	static readonly ID: string = 'workbench.input.flow.settings';

	static readonly RESOURCE = URI.from({ // I think this scheme is invalid, it just shuts up TS
		scheme: 'flow',  // Custom scheme for our editor (try Schemas.https)
		path: 'settings'
	});
	readonly resource = CodeseekSettingsInput.RESOURCE;

	constructor() {
		super();
	}

	override get typeId(): string {
		return CodeseekSettingsInput.ID;
	}

	override getName(): string {
		return nls.localize('codeseekSettingsInputsName', 'Flow\'s Settings');
	}

	override getIcon() {
		return Codicon.checklist; // symbol for the actual editor pane
	}

}


class CodeseekSettingsPane extends EditorPane {
	static readonly ID = 'workbench.test.myCustomPane';

	// private _scrollbar: DomScrollableElement | undefined;

	constructor(
		group: IEditorGroup,
		@ITelemetryService telemetryService: ITelemetryService,
		@IThemeService themeService: IThemeService,
		@IStorageService storageService: IStorageService,
		@IInstantiationService private readonly instantiationService: IInstantiationService
	) {
		super(CodeseekSettingsPane.ID, group, telemetryService, themeService, storageService);
	}

	protected createEditor(parent: HTMLElement): void {
		parent.style.height = '100%';
		parent.style.width = '100%';

		const settingsElt = document.createElement('div');
		settingsElt.style.height = '100%';
		settingsElt.style.width = '100%';

		parent.appendChild(settingsElt);

		// this._scrollbar = this._register(new DomScrollableElement(scrollableContent, {}));
		// parent.appendChild(this._scrollbar.getDomNode());
		// this._scrollbar.scanDomNode();

		// Mount React into the scrollable content
		this.instantiationService.invokeFunction(accessor => {
			const disposables: IDisposable[] | undefined = mountCodeseekSettings(settingsElt, accessor);

			// setTimeout(() => { // this is a complete hack and I don't really understand how scrollbar works here
			// 	this._scrollbar?.scanDomNode();
			// }, 1000)
			disposables?.forEach(d => this._register(d));
		});
	}

	layout(dimension: Dimension): void {
		// if (!settingsElt) return
		// settingsElt.style.height = `${dimension.height}px`;
		// settingsElt.style.width = `${dimension.width}px`;
	}


	override get minimumWidth() { return 700; }

}

// register Settings pane
Registry.as<IEditorPaneRegistry>(EditorExtensions.EditorPane).registerEditorPane(
	EditorPaneDescriptor.create(CodeseekSettingsPane, CodeseekSettingsPane.ID, nls.localize('CodeseekSettingsPane', "Codeseek\'s Settings Pane")),
	[new SyncDescriptor(CodeseekSettingsInput)]
);


// register the gear on the top right
export const CODESEEK_TOGGLE_SETTINGS_ACTION_ID = 'workbench.action.toggleCodeseekSettings';
registerAction2(class extends Action2 {
	constructor() {
		super({
			id: CODESEEK_TOGGLE_SETTINGS_ACTION_ID,
			title: nls.localize2('codeseekSettings', "Flow: Toggle Settings"),
			icon: Codicon.settingsGear,
			menu: [
				{
					id: MenuId.LayoutControlMenuSubmenu,
					group: 'z_end',
				},
				{
					id: MenuId.LayoutControlMenu,
					when: ContextKeyExpr.equals('config.workbench.layoutControl.type', 'both'),
					group: 'z_end'
				}
			]
		});
	}

	async run(accessor: ServicesAccessor): Promise<void> {
		const editorService = accessor.get(IEditorService);
		const editorGroupService = accessor.get(IEditorGroupsService);

		const instantiationService = accessor.get(IInstantiationService);

		// if is open, close it
		const openEditors = editorService.findEditors(CodeseekSettingsInput.RESOURCE); // should only have 0 or 1 elements...
		if (openEditors.length !== 0) {
			const openEditor = openEditors[0].editor;
			const isCurrentlyOpen = editorService.activeEditor?.resource?.fsPath === openEditor.resource?.fsPath;
			if (isCurrentlyOpen)
				await editorService.closeEditors(openEditors);
			else
				await editorGroupService.activeGroup.openEditor(openEditor);
			return;
		}


		// else open it
		const input = instantiationService.createInstance(CodeseekSettingsInput);

		await editorGroupService.activeGroup.openEditor(input);
	}
});



export const CODESEEK_OPEN_SETTINGS_ACTION_ID = 'workbench.action.openCodeseekSettings';
registerAction2(class extends Action2 {
	constructor() {
		super({
			id: CODESEEK_OPEN_SETTINGS_ACTION_ID,
			title: nls.localize2('codeseekSettings', "Flow: Open Settings"),
			f1: true,
			icon: Codicon.settingsGear,
		});
	}
	async run(accessor: ServicesAccessor): Promise<void> {
		const editorService = accessor.get(IEditorService);
		const instantiationService = accessor.get(IInstantiationService);

		// close all instances if found
		const openEditors = editorService.findEditors(CodeseekSettingsInput.RESOURCE);
		if (openEditors.length > 0) {
			await editorService.closeEditors(openEditors);
		}

		// then, open one single editor
		const input = instantiationService.createInstance(CodeseekSettingsInput);
		await editorService.openEditor(input);
	}
});

export const CODESEEK_RESET_SETTINGS_ACTION_ID = 'workbench.action.resetCodeseekSettings';
registerAction2(class extends Action2 {
	constructor() {
		super({
			id: CODESEEK_RESET_SETTINGS_ACTION_ID,
			title: nls.localize2('codeseekSettings', "Flow: Reset Settings"),
			f1: true,
			icon: Codicon.settingsGear,
		});
	}
	async run(accessor: ServicesAccessor): Promise<void> {
		const editorService = accessor.get(IEditorService);
		const settingsService = accessor.get(ICodeseekSettingsService);

		// close all instances if found
		const openEditors = editorService.findEditors(CodeseekSettingsInput.RESOURCE);
		if (openEditors.length > 0) {
			await editorService.closeEditors(openEditors);
		}

		settingsService.resetSettings();
	}
});



// add to settings gear on bottom left
MenuRegistry.appendMenuItem(MenuId.GlobalActivity, {
	group: '0_command',
	command: {
		id: CODESEEK_TOGGLE_SETTINGS_ACTION_ID,
		title: nls.localize('codeseekSettings', "Flow\'s Settings")
	},
	order: 1
});
