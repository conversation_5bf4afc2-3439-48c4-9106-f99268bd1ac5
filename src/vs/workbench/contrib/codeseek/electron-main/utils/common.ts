import fs from 'node:fs/promises'
import syncfs from 'node:fs'
import unzipper from 'unzipper'
import path from 'node:path';
import { execSync } from 'node:child_process';
import { ProviderNames } from '../../common/codeseekSettingsTypes.js';

export const validateJSON = (s: string): { [s: string]: unknown } => {
	try {
		const o = JSON.parse(s);
		return o;
	}
	catch (e) {
		throw new Error(`Tool parameter was not a valid JSON: "${s}".`);
	}
};

export async function fileExists(path: string): Promise<boolean> {
	try {
		const stat = await fs.stat(path)
		return stat.isFile()
	} catch (err: any) {
		if (err.code === 'ENOENT') {
			return false
		}
		//throw on other errors
		throw err
	}
}

export async function unzip(zipFile: string, destinationDir: string): Promise<void> {
	const zip = syncfs.createReadStream(zipFile).pipe(unzipper.Parse({ forceStream: true }))
	for await (const entry of zip) {
		if (entry.path.endsWith('/')) {
			continue
		}
		entry.pipe(syncfs.createWriteStream(path.join(destinationDir, entry.path)))
	}
}

/**
 * Checks if a URL should bypass the proxy based on no_proxy settings
 * @param url - The URL to check
 * @param noProxyValue - The no_proxy environment variable value
 * @returns boolean - True if the URL should bypass the proxy
 */
export function shouldBypassProxy(url: string, noProxyValue: string): boolean {
	// Parse the URL to extract hostname
	let hostname: string;

	try {
		// Try to parse as a complete URL (with protocol)
		const parsedUrl = new URL(url);
		hostname = parsedUrl.hostname;
	} catch (e) {
		// If URL parsing fails, assume the input is a hostname
		hostname = url;
	}

	// Split the no_proxy value into individual entries
	const noProxyEntries = noProxyValue
		.split(',')
		.map(entry => entry.trim())
		.filter(entry => entry.length > 0);

	// Check each no_proxy entry
	for (const entry of noProxyEntries) {
		// Check for IP CIDR notation (e.g., 10.0.0.0/8)
		if (entry.includes('/')) {
			if (isIpInCidrRange(hostname, entry)) {
				return true;
			}
			continue;
		}

		// Check for IP wildcard pattern (e.g., 10.*.*.*)
		if (entry.includes('*') && isIpAddress(hostname)) {
			if (matchesIpWildcard(hostname, entry)) {
				return true;
			}
			continue;
		}

		// Check for domain wildcards (e.g., *.example.com)
		if (entry.startsWith('*.')) {
			const domain = entry.substring(2);
			if (hostname.endsWith(domain) && hostname.length > domain.length) {
				return true;
			}
			continue;
		}

		// Check for other wildcard patterns (e.g., *example.com)
		if (entry.startsWith('*') && !entry.startsWith('*.')) {
			const pattern = entry.substring(1);
			if (hostname.endsWith(pattern)) {
				return true;
			}
			continue;
		}

		// Check for exact match
		if (hostname === entry) {
			return true;
		}

		// Check for domain suffix match (e.g., .example.com matches sub.example.com)
		if (entry.startsWith('.') && hostname.endsWith(entry.substring(1))) {
			return true;
		}
	}

	return false;
}

/**
 * Checks if an IP address is within a CIDR range
 * @param ip - The IP address to check
 * @param cidr - The CIDR range
 * @returns boolean - True if the IP is in the CIDR range
 */
function isIpInCidrRange(ip: string, cidr: string): boolean {
	// Check if the hostname is actually an IP address
	if (!isIpAddress(ip)) {
		return false;
	}

	const [rangeIp, prefixStr] = cidr.split('/');
	const prefix = parseInt(prefixStr, 10);

	// If prefix is invalid, return false
	if (isNaN(prefix) || prefix < 0 || prefix > 32) {
		return false;
	}

	// Convert both IPs to numeric value
	const ipNum = ipToNumber(ip);
	const rangeIpNum = ipToNumber(rangeIp);

	// If either conversion failed, return false
	if (ipNum === null || rangeIpNum === null) {
		return false;
	}

	// Calculate the bit mask for the prefix
	const mask = ~((1 << (32 - prefix)) - 1);

	// Check if the IPs match under the mask
	return (ipNum & mask) === (rangeIpNum & mask);
}

/**
 * Checks if an IP address matches a wildcard pattern like 10.*.*.*
 * @param ip - The IP address to check
 * @param pattern - The wildcard pattern
 * @returns boolean - True if the IP matches the pattern
 */
function matchesIpWildcard(ip: string, pattern: string): boolean {
	// Handle special cases
	if (pattern === '10.*.*.*') {
		// Check if IP starts with '10.'
		return ip.startsWith('10.');
	}

	// Split both IP and pattern into parts
	const ipParts = ip.split('.');
	const patternParts = pattern.split('.');

	// If they don't have the same number of parts, return false
	if (ipParts.length !== patternParts.length) {
		return false;
	}

	// Check each part
	for (let i = 0; i < ipParts.length; i++) {
		if (patternParts[i] !== '*' && patternParts[i] !== ipParts[i]) {
			return false;
		}
	}

	return true;
}

/**
 * Converts an IP address to a numeric value
 * @param ip - The IP address to convert
 * @returns number | null - The numeric value of the IP, or null if invalid
 */
function ipToNumber(ip: string): number | null {
	// Handle special cases like "::1" for IPv6 localhost
	if (ip === "::1") {
		return 0x7F000001; // Same as 127.0.0.1
	}

	const parts = ip.split('.');

	// Check if it's a valid IPv4 address
	if (parts.length !== 4) {
		return null;
	}

	// Convert each octet to a number and validate
	let result = 0;
	for (let i = 0; i < 4; i++) {
		const octet = parseInt(parts[i], 10);

		// Check if each part is a valid number between 0 and 255
		if (isNaN(octet) || octet < 0 || octet > 255) {
			return null;
		}

		result = (result << 8) | octet;
	}

	return result >>> 0; // Ensure unsigned 32-bit
}

/**
 * Checks if a string is a valid IP address
 * @param str - The string to check
 * @returns boolean - True if the string is a valid IP address
 */
function isIpAddress(str: string): boolean {
	// Handle IPv6 localhost
	if (str === "::1") {
		return true;
	}

	// Simple regex for IPv4 validation
	const ipv4Regex = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/;
	const match = str.match(ipv4Regex);

	if (!match) {
		return false;
	}

	// Validate each octet
	for (let i = 1; i <= 4; i++) {
		const octet = parseInt(match[i], 10);
		if (octet < 0 || octet > 255) {
			return false;
		}
	}

	return true;
}

// Example usage
// function testNoProxy() {
// 	const noProxyValue = "::1,10.*.*.*,10.0.0.0/8,*************,***********,**********,***********,************,************,************,***********,************,***********,***********,***********,*************,*************,***********,***********,***********,*********/8,gerrit.zte.com.cn,gitlab.zte.com.cn,localhost,nsdlmirrors.zte.com.cn,*.uac.zte.com.cn,*.xydigit.com,.xydigit.com,xydigit.com,*.zte.com.cn,.zte.com.cn,zte.com.cn,*zte.intra,zte.intra";

// 	const testCases = [
// 		"http://localhost/test",
// 		"http://example.com/test",
// 		"http://***********/test",
// 		"http://127.0.0.1/test",
// 		"http://gerrit.zte.com.cn/repo",
// 		"http://subdomain.zte.com.cn/page",
// 		"http://test.uac.zte.com.cn/api",
// 		"http://username:<EMAIL>:8080/path",
// 		"http://some.random.zte.intra/path",
//         "https://*********",
//         "https://api.deepseek.com/v1/chat"
// 	];

// 	testCases.forEach(url => {
// 		console.log(`URL: ${url} - Bypass Proxy: ${shouldBypassProxy(url, noProxyValue)}`);
// 	});
// }

export const loadSystemEnv = () => {
	try {
		const envOutput = process.platform === 'win32' ? execSync('set').toString() : execSync('env').toString();
		return envOutput.split('\n').reduce((env, line) => {
			const [key, value] = line.split('=');
			if (key && value) {
				env[key] = value.trim();
			}
			return env;
		}, {} as NodeJS.ProcessEnv);
	} catch {
		return {};
	}
};

export type IdeTestConfig = {
	agentSocketUri: string,
	llm: {
		provider: ProviderNames,
		baseURL: string;
		modelName: string;
		apiKey: string;
	};
};

export const env = loadSystemEnv();
