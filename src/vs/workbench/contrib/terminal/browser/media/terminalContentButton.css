/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.terminal-content-button-container {
	position: absolute;
	z-index: 100;
}

.terminal-content-button {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 8px;
	height: 24px;
	border-radius: 4px;
	background-color: var(--vscode-button-secondaryBackground) !important;
	color: var(--vscode-button-secondaryForeground) !important;
	border: 1px solid var(--vscode-button-border, transparent);
	cursor: pointer;
	opacity: 0.8;
	transition: all 0.2s ease-in-out;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.terminal-content-button-text {
	margin-left: 5px;
	font-size: 12px;
	font-weight: 500;
}

.terminal-content-button:hover {
	opacity: 1;
	background-color: var(--vscode-button-secondaryHoverBackground) !important;
	box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
	transform: translateY(-1px);
}

.terminal-content-button:active {
	opacity: 0.9;
	transform: translateY(0);
	box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}
