/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { KeyCode, KeyMod } from '../../../../base/common/keyCodes.js';
import { Action2, registerAction2 } from '../../../../platform/actions/common/actions.js';
import { ServicesAccessor } from '../../../../platform/instantiation/common/instantiation.js';
import { KeybindingWeight } from '../../../../platform/keybinding/common/keybindingsRegistry.js';
import { localize2 } from '../../../../nls.js';
import { ICodeEditorService } from '../../../../editor/browser/services/codeEditorService.js';
import { IEditCodeService } from './editCodeService.js';
import { CODESEEK_ACCEPT_ALL_DIFFS_ACTION_ID, CODESEEK_ACCEPT_DIFF_ACTION_ID, CODESEEK_REJECT_ALL_DIFFS_ACTION_ID, CODESEEK_REJECT_DIFF_ACTION_ID } from './actionIDs.js';
// import { IMetricsService } from '../common/metricsService.js';

// 注册接受当前差异的操作
registerAction2(class extends Action2 {
	constructor() {
		super({
			id: CODESEEK_ACCEPT_DIFF_ACTION_ID,
			title: localize2('codeseekAcceptDiff', 'Codeseek: Accept Current Diff'),
			f1: true,
			keybinding: {
				primary: KeyMod.CtrlCmd | KeyMod.Alt | KeyCode.KeyY,
				weight: KeybindingWeight.CodeseekExtension,
			}
		});
	}

	async run(accessor: ServicesAccessor): Promise<void> {
		const editorService = accessor.get(ICodeEditorService);
		const editCodeService = accessor.get(IEditCodeService);
		// const metricsService = accessor.get(IMetricsService);

		const editor = editorService.getActiveCodeEditor();
		if (!editor) {
			return;
		}

		const uri = editor.getModel()?.uri;
		if (!uri) {
			return;
		}
		const currentDiffIndex = editCodeService.getCurrentDiffIndex();
		editCodeService.acceptDiffAtIndex({ uri, index: currentDiffIndex });
		// metricsService.capture('Accept Diff via Shortcut', {});
	}
});

// 注册拒绝当前差异的操作
registerAction2(class extends Action2 {
	constructor() {
		super({
			id: CODESEEK_REJECT_DIFF_ACTION_ID,
			title: localize2('codeseekRejectDiff', 'Codeseek: Reject Current Diff'),
			f1: true,
			keybinding: {
				primary: KeyMod.CtrlCmd | KeyMod.Alt | KeyCode.KeyN,
				weight: KeybindingWeight.CodeseekExtension,
			}
		});
	}

	async run(accessor: ServicesAccessor): Promise<void> {
		const editorService = accessor.get(ICodeEditorService);
		const editCodeService = accessor.get(IEditCodeService);
		// const metricsService = accessor.get(IMetricsService);

		const editor = editorService.getActiveCodeEditor();
		if (!editor) {
			return;
		}

		const uri = editor.getModel()?.uri;
		if (!uri) {
			return;
		}
		const currentDiffIndex = editCodeService.getCurrentDiffIndex();
		editCodeService.rejectDiffAtIndex({ uri, index: currentDiffIndex });
		// metricsService.capture('Reject Diff via Shortcut', {});
	}
});

registerAction2(class extends Action2 {
	constructor() {
		super({
			id: CODESEEK_ACCEPT_ALL_DIFFS_ACTION_ID,
			title: localize2('codeseekAcceptAllDiffs', 'Codeseek: Accept All Diffs'),
			f1: true,
			keybinding: {
				primary: KeyMod.Alt | KeyMod.Shift | KeyCode.Enter,
				weight: KeybindingWeight.CodeseekExtension,
			}
		});
	}

	async run(accessor: ServicesAccessor): Promise<void> {
		const editorService = accessor.get(ICodeEditorService);
		const editCodeService = accessor.get(IEditCodeService);
		const editor = editorService.getActiveCodeEditor();
		if (!editor) {
			return;
		}
		const activeUrl = editor.getModel()?.uri;
		if (activeUrl) {
			editCodeService.removeDiffAreas({
				uri: activeUrl,
				removeCtrlKs: false,
				behavior: 'accept'
			});
		}
	}
});

registerAction2(class extends Action2 {
	constructor() {
		super({
			id: CODESEEK_REJECT_ALL_DIFFS_ACTION_ID,
			title: localize2('codeseekRejectAllDiffs', 'Codeseek: Reject All Diffs'),
			f1: true,
			keybinding: {
				primary: KeyMod.Alt | KeyMod.Shift | KeyCode.Backspace,
				weight: KeybindingWeight.CodeseekExtension,
			}
		});
	}

	async run(accessor: ServicesAccessor): Promise<void> {
		const editCodeService = accessor.get(IEditCodeService);
		const editorService = accessor.get(ICodeEditorService);
		const editor = editorService.getActiveCodeEditor();
		if (!editor) {
			return;
		}
		const activeUrl = editor.getModel()?.uri;

		if (activeUrl) {
			editCodeService.removeDiffAreas({
				uri: activeUrl,
				removeCtrlKs: false,
				behavior: 'reject'
			});
		}
	}
});
