import { Event } from '../../../../../base/common/event.js';
import { URI } from '../../../../../base/common/uri.js';
import { IServerChannel } from '../../../../../base/parts/ipc/common/ipc.js';
import { IClangdMainService } from '../../common/clangdMainService.js';

/**
 * ClangdSymbol IPC 通道 - 处理主进程和渲染进程之间的通信
 */
export class ClangdSymbolIpcChannel implements IServerChannel {

	constructor(
		private readonly clangdMainService: IClangdMainService
	) {
	}

	listen<T>(_: unknown, event: string, arg?: any): Event<T> {
		switch (event) {
			default:
				throw new Error(`Unknown event: ${event}`);
		}
	}

	/**
	 * 处理方法调用
	 */
	call(_: unknown, command: string, args: any[] = []): Promise<any> {
		let scopeDir = undefined;
		let filePath = undefined;
		switch (command) {
			case 'getSymbolReferences':
				scopeDir = URI.parse(args[0].path);
				filePath = URI.parse(args[1].path);
				return this.clangdMainService.getSymbolReferences(scopeDir,
					{
						file: filePath,
						line: args[2],
						character: args[3]
					});
			case 'initialize':
				scopeDir = URI.parse(args[0].path);
				return this.clangdMainService.initialize(scopeDir, args[1]);
			case 'didOpen':
				scopeDir = URI.parse(args[0].path);
				const documentUri = args[1] as string;
				return this.clangdMainService.didOpen(scopeDir, documentUri);
			default:
				throw new Error(`Unknown command: ${command}`);
		}
	}
}
