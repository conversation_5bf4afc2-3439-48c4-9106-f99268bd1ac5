import { spawn } from 'node:child_process';
import * as os from 'os';
import * as path from 'path';
import { isWindows } from '../../../../../base/common/platform.js';
import { URI } from '../../../../../base/common/uri.js';
import { findCtagsPath } from './findCtags.js';
import { Mutex } from 'async-mutex';
import { mkdirp } from 'mkdirp';
import fs from 'node:fs';
import { access, rename, rm, stat, writeFile } from 'node:fs/promises';
import { createInterface } from 'node:readline';
import { CancellationToken, CancellationTokenSource } from '../../../../../base/common/cancellation.js';
import { Emitter } from '../../../../../base/common/event.js';
import { IDisposable } from '../../../../../base/common/lifecycle.js';
import { ICodeseekLogger } from '../../common/codeseekLogService.js';

/**
 * 符号定义结果接口
 */
export interface SymbolDefinition {
	rawLineContent: string; // 原始行内容
	name: string;           // 符号名称
	path: string;           // 文件路径
	scopePath: string;		// 根路径
	line: number;           // 行号
	kind: string;           // 类型 (如 function, class, variable)
	language: string;       // 语言
	pattern?: string;       // 匹配模式
	scope?: string;         // 作用域
	file: URI;              // 文件URI
	positions?: [number, number]; // 位置信息
	parentSymbol?: string;  // 父符号（如结构体或枚举）
}

export type FileURI = Omit<URI, 'fsPath'> & {
	scheme: 'file'
	/**
	 * 文件系统路径
	 */
	fsPath: string
}

/**
 * 检测文件更改信息
 */
export interface IndexStatus {
	lastModified: number;       // 最后修改时间
	fileCount: number;          // 文件数量
	changedFiles?: string[];    // 变更的文件
	lastIndexTime?: number;     // 上次索引时间
}

export function isFileURI(uri: URI): uri is FileURI {
	return uri.scheme === 'file';
}

export function assertFileURI(uri: URI): FileURI {
	if (!isFileURI(uri)) {
		throw new TypeError(`assertFileURI failed on ${uri.toString()}`);
	}
	return uri;
}

const endPattern = '/;"'

/**
 * 解析ctags输出格式为SymbolDefinition对象数组
 * 从标签文件中读取 _TAG_KIND_DESCRIPTION 定义，并根据不同语言进行映射
 */
function parseCtagsOutput(lines: string[], scopeDir: FileURI): SymbolDefinition[] {
	if (lines.length === 0) {
		return [];
	}

	// 解析标签文件的种类描述定义
	// 格式: !_TAG_KIND_DESCRIPTION!{language}<TAB>{letter},{name}<TAB>/{description}/
	const kindDescriptions = new Map<string, Map<string, string>>();
	// 修改正则表达式以匹配制表符分隔的格式
	const kindDescriptionRegex = /^!_TAG_KIND_DESCRIPTION!([a-zA-Z0-9_+]+)\t([a-zA-Z0-9]),([a-zA-Z0-9_]+)\t\/(.+)\/$/;

	// 首先处理标签种类描述
	for (const line of lines) {
		if (line.startsWith('!_TAG_KIND_DESCRIPTION!')) {
			const match = line.match(kindDescriptionRegex);
			if (match) {
				const language = match[1];     // 语言，如 "C" 或 "C++"
				const letter = match[2];       // 类型字母，如 "d"
				const kindName = match[3];     // 类型名称，如 "macro"
				// const description = match[4];  // 类型描述，如 "macro definitions"

				if (!kindDescriptions.has(language)) {
					kindDescriptions.set(language, new Map<string, string>());
				}

				const languageMap = kindDescriptions.get(language);
				if (languageMap) {
					languageMap.set(letter, kindName);
				}
			}
		}
	}

	// 提取符号定义行（非元数据行）
	const symbolLines = lines.filter(line =>
		line && !line.startsWith('!') // 过滤掉元数据行
	);

	// 处理索引行，根据优先级选择最合适的索引行
	// 模拟 process_index_line 函数的行为
	const processIndexLines = (indexLines: string[]): string[] => {
		// 实际项目中可能需要根据具体需求调整路径优先级
		// 这里简化处理，可以根据需要扩展
		return indexLines;
	};

	const processedLines = processIndexLines(symbolLines);

	return processedLines.map(line => {
		// 使用更可靠的方法解析ctags行
		const parsedLine = parseCtagsLine(line);
		if (!parsedLine) {
			return {
				rawLineContent: line,
			} as SymbolDefinition;
		}

		const { name, filePath, pattern, kind, extFields } = parsedLine;

		// 获取行号信息
		const lineNumber = extFields.has('line') ? parseInt(extFields.get('line')!, 10) : 0;

		// 获取语言信息
		const language = extFields.has('language') ? extFields.get('language')! : 'unknown';

		// 获取作用域信息
		let scope: string | undefined = undefined;
		let parentSymbol: string | undefined = undefined;

		// 处理模式和位置信息
		let patternText = '';
		let columnNumber = 1;

		// 提取模式内容，通常是 /^.../;"
		if (pattern.startsWith('/^') && pattern.includes(endPattern)) {
			patternText = pattern.substring(2, pattern.indexOf(endPattern));

			// 尝试查找符号在行内的位置
			const symbolIndex = patternText.indexOf(name);
			if (symbolIndex >= 0) {
				columnNumber = symbolIndex + 1; // +1 与Python的1-based索引保持一致
			}
		}

		// 映射类型
		let mappedKind: string = kind;

		// 尝试使用语言特定的映射
		if (language !== 'unknown' && kindDescriptions.has(language)) {
			const languageMap = kindDescriptions.get(language);
			if (languageMap && languageMap.has(kind)) {
				mappedKind = languageMap.get(kind) || kind;
			}
		}

		const result: SymbolDefinition = {
			rawLineContent: line,
			name,
			path: filePath,
			scopePath: scopeDir.fsPath,
			line: lineNumber,
			kind: mappedKind,
			language,
			pattern: patternText,
			scope,
			file: URI.file(filePath),
			positions: [lineNumber, columnNumber] as [number, number],
			parentSymbol
		};

		return result;
	}).filter((item): item is SymbolDefinition => item !== null);
}

/**
 * 解析ctags输出行，更可靠地处理可能包含制表符的模式
 * @param line ctags 输出行
 * @returns 解析后的字段对象，包含名称、文件路径、模式、类型和扩展字段
 */
function parseCtagsLine(line: string): {
	name: string,
	filePath: string,
	pattern: string,
	kind: string,
	extFields: Map<string, string>
} | null {
	// 格式: 名称<TAB>文件路径<TAB>模式$/;"<TAB>类型<TAB>扩展字段

	// 名称 (第一个字段)
	const firstTabIndex = line.indexOf('\t');
	if (firstTabIndex === -1) return null;

	const name = line.substring(0, firstTabIndex);

	// 文件路径 (第二个字段)
	const secondTabIndex = line.indexOf('\t', firstTabIndex + 1);
	if (secondTabIndex === -1) return null;

	const filePath = line.substring(firstTabIndex + 1, secondTabIndex);

	// 模式 - 从第二个制表符后到 ;"
	const patternEnd = line.indexOf(endPattern, secondTabIndex);
	if (patternEnd === -1) return null;

	const pattern = line.substring(secondTabIndex + 1, patternEnd + endPattern.length);

	// 剩余部分 (类型和扩展字段)
	const remainder = line.substring(patternEnd + endPattern.length);
	const parts = remainder.split('\t').filter(p => p);

	if (parts.length === 0) return null;

	const kind = parts[0];

	// 解析扩展字段 (格式: 键:值)
	const extFields = new Map<string, string>();
	for (let i = 1; i < parts.length; i++) {
		const part = parts[i];
		const colonIndex = part.indexOf(':');
		if (colonIndex !== -1) {
			const key = part.substring(0, colonIndex);
			const value = part.substring(colonIndex + 1);
			extFields.set(key, value);
		}
	}

	return { name, filePath, pattern, kind, extFields };
}

async function fileExists(file: URI): Promise<boolean> {
	if (!isFileURI(file)) {
		throw new Error('only file URIs are supported');
	}
	try {
		await access(file.fsPath, fs.constants.F_OK);
		return true;
	} catch {
		return false;
	}
}

/**
 * 索引选项接口
 */
interface IndexOptions {
	retryIfLastAttemptFailed: boolean;
	ignoreExisting: boolean;
	languages?: string[];  // 可选的语言过滤器
}

/**
 * 索引开始事件
 */
export interface IndexStartEvent {
	scopeDir: FileURI;
	cancel: () => void;
	done: Promise<void>;
}

/**
 * 索引结束事件
 */
export interface IndexEndEvent {
	scopeDir: FileURI;
}

/**
 * Ctags运行器类，负责管理ctags工具的执行和索引
 */
export class CtagsRunner implements IDisposable {
	public dispose(): void {
		this.indexLocks.clear();
	}

	// 索引根目录
	private indexRoot: FileURI;
	private indexLocks: Map<string, RWLock> = new Map();

	private status: IndexStatusTracker = new IndexStatusTracker();

	constructor(
		private logger: ICodeseekLogger,
		private dataFolderName: string
	) {
		const globalStoragePath = path.join(os.homedir(), this.dataFolderName, 'ctags', 'indexroot');
		const indexRoot = URI.file(globalStoragePath);

		if (!isFileURI(indexRoot)) {
			throw new Error('ctags only supports running on the file system');
		}
		this.indexRoot = indexRoot;
	}

	public onIndexStart(cb: (e: IndexStartEvent) => void): IDisposable {
		return this.status.onDidStart(cb);
	}

	public onIndexEnd(cb: (e: IndexEndEvent) => void): IDisposable {
		return this.status.onDidEnd(cb);
	}

	private async mustCtagsPath(): Promise<string> {
		const ctagsPath = await findCtagsPath(this.logger, this.dataFolderName);
		if (!ctagsPath) {
			throw new Error('No ctags executable');
		}
		return ctagsPath;
	}

	/**
	 * 获取符号定义
	 * @param symbolName 符号名称
	 * @param scopeDirs 目录范围
	 * @param filter 过滤条件
	 */
	public async getSymbolDefinitions(
		symbolName: string,
		scopeDirs: URI[],
		filter?: {
			kind?: string[],
			language?: string[],
			path?: string
		}
	): Promise<SymbolDefinition[]> {
		this.logger.debug(`getSymbolDefinitions: ${symbolName} scopeDirs: ${scopeDirs.map(s => s.toString()).join(', ')}`);
		const promises = scopeDirs
			.filter(isFileURI)
			.map(scopeDir => this.getSymbolDefinitionsForScopeDir(symbolName, scopeDir, filter));

		const results = await Promise.all(promises);
		return results.flat(); // 扁平化结果数组
	}

	/**
	 * 为特定目录查询符号定义
	 */
	private async getSymbolDefinitionsForScopeDir(
		symbolName: string,
		scopeDir: FileURI,
		filter?: {
			kind?: string[],
			language?: string[],
			path?: string
		}
	): Promise<SymbolDefinition[]> {
		const maxRetries = 5;

		// 循环尝试，防止在查询过程中索引被删除
		for (let i = 0; i < maxRetries; i++) {
			await this.getIndexLock(scopeDir).withWrite(async () => {
				await this.unsafeEnsureIndex(scopeDir, {
					retryIfLastAttemptFailed: i === 0,
					ignoreExisting: false
				});
			});

			let indexNotFound = false;
			const definitions = await this.getIndexLock(scopeDir).withRead(async () => {
				// 再次检查索引是否存在
				if (!(await this.unsafeIndexExists(scopeDir))) {
					indexNotFound = true;
					return [];
				}
				return await this.unsafeQuerySymbol(symbolName, scopeDir);
			});

			if (indexNotFound) {
				continue;
			}

			return definitions;
		}

		throw new Error(`failed to find index after ${maxRetries} tries for directory ${scopeDir}`);
	}

	/**
	 * 删除索引
	 */
	public async deleteIndex(scopeDir: FileURI): Promise<void> {
		await this.getIndexLock(scopeDir).withWrite(async () => {
			await this.unsafeDeleteIndex(scopeDir);
		});
	}

	/**
	 * 获取索引状态
	 */
	public async getIndexStatus(
		scopeDir: FileURI
	): Promise<'unindexed' | 'indexing' | 'ready' | 'failed'> {
		if (this.status.isInProgress(scopeDir)) {
			return 'indexing';
		}
		const hasIndex = await this.getIndexLock(scopeDir).withRead(async () => {
			return this.unsafeIndexExists(scopeDir);
		});
		if (hasIndex) {
			return 'ready';
		}
		if (await this.didIndexFail(scopeDir)) {
			return 'failed';
		}
		return 'unindexed';
	}

	/**
	 * 检查索引是否过期并在必要时重新索引
	 */
	public async reindexIfStale(scopeDir: FileURI): Promise<void> {
		this.logger.info('CtagsRunner', 'reindexIfStale', scopeDir.fsPath);
		try {
			const status = await this.checkIndexStatus(scopeDir);
			if (!status) {
				await this.ensureIndex(scopeDir, {
					retryIfLastAttemptFailed: false,
					ignoreExisting: false
				});
				return;
			}

			// 如果文件数量发生变化或者有文件被修改，则重新索引
			if (this.shouldReindex(status)) {
				await this.ensureIndex(scopeDir, {
					retryIfLastAttemptFailed: false,
					ignoreExisting: true
				});
			}
		} catch (error) {
			this.logger.error('CtagsRunner', `Error checking freshness of index at ${scopeDir.fsPath}`, error);
		}
	}

	/**
	 * 检查索引状态
	 */
	private async checkIndexStatus(scopeDir: FileURI): Promise<IndexStatus | null> {
		try {
			const { indexFile } = this.getIndexPaths(scopeDir);

			if (!(await fileExists(indexFile))) {
				return null;
			}

			const statInfo = await stat(indexFile.fsPath);
			const lastModified = statInfo.mtimeMs;

			// 读取元信息文件，如果存在
			const metaFile = URI.file(`${indexFile.fsPath}.meta`);
			let metaInfo: Partial<IndexStatus> = {};

			if (await fileExists(metaFile)) {
				try {
					const metaContent = await fs.promises.readFile(metaFile.fsPath, 'utf-8');
					metaInfo = JSON.parse(metaContent);
				} catch (err) {
					this.logger.error('CtagsRunner', 'Error reading meta info', err);
				}
			}

			return {
				lastModified,
				fileCount: metaInfo.fileCount || 0,
				changedFiles: metaInfo.changedFiles,
				lastIndexTime: metaInfo.lastIndexTime
			};
		} catch (error) {
			this.logger.error('CtagsRunner', 'Error checking index status', error);
			return null;
		}
	}

	/**
	 * 确定是否需要重新索引
	 */
	private shouldReindex(status: IndexStatus): boolean {
		// 如果没有上次索引时间，则需要重新索引
		if (!status.lastIndexTime) {
			return true;
		}

		// 如果有变更的文件，则重新索引
		if (status.changedFiles && status.changedFiles.length > 0) {
			return true;
		}

		// 检查最后修改时间是否超过阈值（1小时）
		const oneHour = 60 * 60 * 1000;
		const now = Date.now();
		if (now - status.lastIndexTime > oneHour) {
			return true;
		}

		return false;
	}

	/**
	 * 创建索引
	 */
	public async ensureIndex(
		scopeDir: FileURI,
		options: IndexOptions = { retryIfLastAttemptFailed: false, ignoreExisting: false }
	): Promise<void> {
		await this.getIndexLock(scopeDir).withWrite(async () => {
			await this.unsafeEnsureIndex(scopeDir, options);
		});
	}

	private getIndexLock(scopeDir: FileURI): RWLock {
		const { indexDir } = this.getIndexPaths(scopeDir);
		let lock = this.indexLocks.get(indexDir.toString());
		if (lock) {
			return lock;
		}
		lock = new RWLock();
		this.indexLocks.set(indexDir.toString(), lock);
		return lock;
	}

	/**
	 * 查询符号定义（不安全版本，需要由锁保护）
	 */
	private async unsafeQuerySymbol(
		symbolName: string,
		scopeDir: FileURI
	): Promise<SymbolDefinition[]> {
		const { indexFile } = this.getIndexPaths(scopeDir);

		if (!(await fileExists(indexFile))) {
			return [];
		}

		try {
			this.logger.debug('CtagsRunner', 'querying ctags', symbolName);
			// 使用 Node.js 流处理替代 grep 命令
			const stream = fs.createReadStream(indexFile.fsPath, { encoding: 'utf-8' });
			const lines: string[] = [];
			const symbolRegex = new RegExp(`^${symbolName}\\s+.*;"\\s+`);
			const kindDescriptionRegex = /^!_TAG_KIND_DESCRIPTION!/;

			if (isWindows) {
				// 创建readline接口以行为单位处理
				const rl = createInterface({
					input: stream,
					crlfDelay: Infinity
				});

				// 逐行处理：收集符号行和元数据行
				for await (const line of rl) {
					if (symbolRegex.test(line) || kindDescriptionRegex.test(line)) {
						lines.push(line);
					}
				}
			} else {
				// 先获取元数据
				try {
					const kindDescProcess = spawn('grep', ['-a', '-E', '^\\!_TAG_KIND_DESCRIPTION\\!', indexFile.fsPath]);
					const kindDescRl = createInterface({
						input: kindDescProcess.stdout,
						crlfDelay: Infinity
					});

					for await (const line of kindDescRl) {
						lines.push(line);
					}
				} catch (err) {
					this.logger.debug('CtagsRunner', 'No kind description metadata found');
				}

				// 获取符号匹配行
				const symbolProcess = spawn('grep', ['-a', '-E', `^${symbolName}\\s+`, indexFile.fsPath]);

				const rl = createInterface({
					input: symbolProcess.stdout,
					crlfDelay: Infinity
				});

				for await (const line of rl) {
					lines.push(line);
				}
			}

			// 解析索引输出为符号定义对象
			let results = parseCtagsOutput(lines, scopeDir);
			this.logger.debug('CtagsRunner', `query ctags ${symbolName} result: `, JSON.stringify(results));

			return results;
		} catch (error) {
			this.logger.error('CtagsRunner', `Error querying ctags: ${error}`);
			return [];
		}
	}

	/**
	 * 删除索引（不安全版本，需要由锁保护）
	 */
	private async unsafeDeleteIndex(scopeDir: FileURI): Promise<void> {
		const trashRootDir = URI.joinPath(this.indexRoot, '.trash');
		// 使用 fs.mkdir 替代 mkdirp
		await mkdirp(trashRootDir.fsPath);
		const { indexDir } = this.getIndexPaths(scopeDir);

		if (!(await fileExists(indexDir))) {
			// 索引目录不存在，无需操作
			return;
		}

		// 为垃圾目录创建一个唯一名称
		const trashDir = URI.joinPath(trashRootDir, `${path.basename(indexDir.path)}-${Date.now()}`);
		if (await fileExists(trashDir)) {
			// 如果目标垃圾文件夹已存在，报错
			throw new Error(
				`could not delete index ${indexDir}: target trash directory ${trashDir} already exists`
			);
		}

		await rename(indexDir.fsPath, trashDir.fsPath);
		rm(trashDir.fsPath, { recursive: true, force: true }).catch(() => { }); // 在后台删除
	}

	/**
	 * 判断索引是否存在（不安全版本，需要由锁保护）
	 */
	private async unsafeIndexExists(scopeDir: FileURI): Promise<boolean> {
		const { indexFile } = this.getIndexPaths(scopeDir);
		return fileExists(indexFile);
	}

	/**
	 * 确保索引存在（不安全版本，需要由锁保护）
	 */
	private async unsafeEnsureIndex(scopeDir: FileURI, options: IndexOptions): Promise<void> {
		this.logger.debug('CtagsRunner', 'unsafeEnsureIndex', scopeDir.toString(), { verbose: { options } });
		if (!options.ignoreExisting) {
			const indexExists = await this.unsafeIndexExists(scopeDir);
			if (indexExists) {
				return;
			}
		}

		if (!options.retryIfLastAttemptFailed && (await this.didIndexFail(scopeDir))) {
			// 索引构建先前失败，不尝试重建
			this.logger.debug(
				'ctags',
				'index build previously failed and retryIfLastAttemptFailed=false, not rebuilding'
			);
			return;
		}

		const { indexDir, tmpDir } = this.getIndexPaths(scopeDir);
		try {
			await this.unsafeUpsertIndex(indexDir, tmpDir, scopeDir, options);
		} catch (error) {
			this.logger.debug('ctags', 'ctags index creation failed', error);
			await this.markIndexFailed(scopeDir);
			throw error;
		}
		await this.clearIndexFailure(scopeDir);
	}

	/**
	 * 获取索引路径
	 */
	private getIndexPaths(scopeDir: FileURI): {
		indexDir: FileURI;
		tmpDir: FileURI;
		indexFile: FileURI;
	} {
		let indexSubdir = scopeDir.path;

		// Windows 路径处理
		if (isWindows) {
			if (indexSubdir[2] === ':') {
				indexSubdir = indexSubdir.slice(0, 2) + indexSubdir.slice(3);
			} else if (indexSubdir.slice(2, 5) === '%3A') {
				indexSubdir = indexSubdir.slice(0, 2) + indexSubdir.slice(5);
			}
		}

		const indexDir = assertFileURI(URI.joinPath(this.indexRoot, indexSubdir));
		const tmpDir = assertFileURI(URI.joinPath(this.indexRoot, '.tmp', indexSubdir));
		const indexFile = assertFileURI(URI.joinPath(indexDir, 'tags'));

		return {
			indexDir,
			tmpDir,
			indexFile
		};
	}

	/**
	 * 更新索引
	 */
	private unsafeUpsertIndex(
		indexDir: FileURI,
		tmpIndexDir: FileURI,
		scopeDir: FileURI,
		options: IndexOptions
	): Promise<void> {
		const cancellation = new CancellationTokenSource();
		const upsert = this._unsafeUpsertIndex(indexDir, tmpIndexDir, scopeDir, options, cancellation.token);
		this.status.didStart({ scopeDir, done: upsert, cancel: () => cancellation.cancel() });
		void upsert.finally(() => {
			this.status.didEnd({ scopeDir });
			cancellation.dispose();
		});
		return upsert;
	}

	/**
	 * 执行索引更新操作
	 */
	private async _unsafeUpsertIndex(
		indexDir: FileURI,
		tmpIndexDir: FileURI,
		scopeDir: FileURI,
		options: IndexOptions,
		cancellationToken: CancellationToken
	): Promise<void> {
		const ctagsPath = await this.mustCtagsPath();
		if (!ctagsPath) {
			return;
		}

		// 清理临时目录
		await rm(tmpIndexDir.fsPath, { recursive: true }).catch(() => undefined);
		await mkdirp(tmpIndexDir.fsPath);

		this.logger.debug('ctags', 'creating index', indexDir);

		const disposeOnFinish: IDisposable[] = [];
		if (cancellationToken.isCancellationRequested) {
			throw new Error('Operation aborted');
		}

		let wasCancelled = false;
		let onExit: (() => void) | undefined;

		try {
			// ctags --fields=+n -R --languages=C --langmap=C:+.h -f  {line}/../{self.index_file} {line}/.. ; chmod +x {line}/../{self.index_file}
			// 构建ctags命令行参数
			const args = [
				'--tag-relative=never',
				'--fields=+n,l',
				'-R',
				'--langmap=C:+.h',
				'--langmap=C++:+.hpp,.hxx,.cpp,.cc',
				'-f', `${tmpIndexDir.fsPath}/tags`
			];

			// 如果指定了语言过滤器，则添加--languages参数
			if (options.languages && options.languages.length > 0) {
				args.push(`--languages=${options.languages.join(',')}`);
			}

			// 最后添加目标目录
			args.push(scopeDir.fsPath);

			this.logger.debug(`ctags building index: ${ctagsPath} ${args.join(' ')}`);

			const proc = spawn(ctagsPath, args, {
				env: { ...process.env },
				stdio: ['ignore', 'pipe', 'pipe']
			});

			onExit = () => {
				proc.kill('SIGKILL');
			};
			process.on('exit', onExit);

			if (cancellationToken.isCancellationRequested) {
				wasCancelled = true;
				proc.kill('SIGKILL');
			} else {
				disposeOnFinish.push(
					cancellationToken.onCancellationRequested(() => {
						wasCancelled = true;
						proc.kill('SIGKILL');
					})
				);
			}

			// 收集输出以进行日志记录
			let stdoutData = '';
			let stderrData = '';
			proc.stdout.on('data', (data) => {
				stdoutData += data.toString();
			});
			proc.stderr.on('data', (data) => {
				stderrData += data.toString();
			});

			// 等待进程完成
			await new Promise<void>((resolve, reject) => {
				proc.on('error', reject);
				proc.on('exit', code => {
					if (code === 0) {
						resolve();
					} else {
						const error = new Error(`ctags exited with code ${code}, stderr: ${stderrData}`);
						this.logger.error('ctags', error.message);
						reject(error);
					}
				});
			});

			this.logger.debug(`ctags index builded.`);

			// 存储索引元数据
			const fileCount = await this.countFiles(scopeDir.fsPath);
			const metaData: IndexStatus = {
				lastModified: Date.now(),
				fileCount,
				lastIndexTime: Date.now(),
				changedFiles: []
			};

			const metaFile = path.join(tmpIndexDir.fsPath, 'tags.meta');
			await writeFile(metaFile, JSON.stringify(metaData, null, 2));

			// 移动到最终索引路径
			await rm(indexDir.fsPath, { recursive: true }).catch(() => undefined);
			// 使用 fs.mkdir 替代 mkdirp
			await mkdirp(path.dirname(indexDir.fsPath));
			await rename(tmpIndexDir.fsPath, indexDir.fsPath);
		} catch (error) {
			if (wasCancelled) {
				throw new Error('Operation aborted');
			}
			throw new Error(`Ctags error: ${error instanceof Error ? error.message : String(error)}`);
		} finally {
			if (onExit) {
				process.removeListener('exit', onExit);
				disposeOnFinish.forEach(disposable => disposable.dispose());
				await rm(tmpIndexDir.fsPath, { recursive: true, force: true }).catch(() => { });
			}
		}
	}

	/**
	 * 统计目录中文件数量，兼容 Windows 和 Linux 平台
	 */
	private async countFiles(dir: string): Promise<number> {
		try {
			if (isWindows) {
				// Windows 平台使用 Node.js fs API 递归统计文件
				return await this.countFilesWithNodeAPI(dir);
			} else {
				// 使用流式处理的 find 命令
				return new Promise((resolve, reject) => {
					let count = 0;
					const findProcess = spawn('find', [dir, '-type', 'f', '-not', '-path', '*/\\.*']);

					const rl = createInterface({
						input: findProcess.stdout,
						crlfDelay: Infinity
					});

					rl.on('line', () => {
						count++;
					});

					rl.on('close', () => {
						resolve(count);
					});

					findProcess.on('error', (err) => {
						reject(err);
					});

					findProcess.stderr.on('data', (data) => {
						this.logger.warn('CtagsRunner', `find stderr: ${data}`);
					});
				});
			}
		} catch (error) {
			this.logger.error('CtagsRunner', 'Error counting files', error);
			return 0;
		}
	}

	/**
	 * 使用 Node.js fs API 递归统计文件数量，用于 Windows 平台
	 */
	private async countFilesWithNodeAPI(dir: string): Promise<number> {
		let fileCount = 0;

		// 处理目录函数
		const processDir = async (currentDir: string) => {
			try {
				const entries = await fs.promises.readdir(currentDir, { withFileTypes: true });

				for (const entry of entries) {
					const fullPath = path.join(currentDir, entry.name);

					// 跳过隐藏文件和目录
					if (entry.name.startsWith('.')) {
						continue;
					}

					if (entry.isDirectory()) {
						// 递归处理子目录
						await processDir(fullPath);
					} else if (entry.isFile()) {
						// 增加文件计数
						fileCount++;
					}
				}
			} catch (err) {
				// 忽略访问错误，继续计数
				this.logger.debug('CtagsRunner', `Could not access directory: ${currentDir}`, err);
			}
		};

		await processDir(dir);
		return fileCount;
	}

	/**
	 * 标记索引失败
	 */
	private async markIndexFailed(scopeDir: FileURI): Promise<void> {
		const failureRoot = URI.joinPath(this.indexRoot, '.failed');
		// 使用 fs.mkdir 替代 mkdirp
		await mkdirp(failureRoot.fsPath);
		const failureSentinelFile = URI.joinPath(failureRoot, scopeDir.path.replaceAll('/', '__'));
		await writeFile(failureSentinelFile.fsPath, '');
	}

	/**
	 * 判断索引是否失败
	 */
	private async didIndexFail(scopeDir: FileURI): Promise<boolean> {
		const failureRoot = URI.joinPath(this.indexRoot, '.failed');
		const failureSentinelFile = URI.joinPath(failureRoot, scopeDir.path.replaceAll('/', '__'));
		return fileExists(failureSentinelFile);
	}

	/**
	 * 清除索引失败标记
	 */
	private async clearIndexFailure(scopeDir: FileURI): Promise<void> {
		const failureRoot = URI.joinPath(this.indexRoot, '.failed');
		const failureSentinelFile = URI.joinPath(failureRoot, scopeDir.path.replaceAll('/', '__'));
		await rm(failureSentinelFile.fsPath, { force: true }).catch(() => { });
	}
}

/**
 * 索引状态跟踪器
 */
class IndexStatusTracker implements IDisposable {
	private startEmitter = new Emitter<IndexStartEvent>();
	private stopEmitter = new Emitter<IndexEndEvent>();
	private inProgressDirs = new Set<string /* uri.toString() */>();

	public dispose(): void {
		this.startEmitter.dispose();
		this.stopEmitter.dispose();
	}

	public didStart(event: IndexStartEvent): void {
		this.inProgressDirs.add(event.scopeDir.toString());
		this.startEmitter.fire(event);
	}

	public didEnd(event: IndexEndEvent): void {
		this.inProgressDirs.delete(event.scopeDir.toString());
		this.stopEmitter.fire(event);
	}

	public onDidStart(cb: (e: IndexStartEvent) => void): IDisposable {
		return this.startEmitter.event(cb);
	}

	public onDidEnd(cb: (e: IndexEndEvent) => void): IDisposable {
		return this.stopEmitter.event(cb);
	}

	public isInProgress(scopeDir: FileURI): boolean {
		return this.inProgressDirs.has(scopeDir.toString());
	}
}

/**
 * 读写锁，保证索引操作的并发安全
 */
class RWLock {
	/**
	 * 不变量：
	 * - 如果 readers > 0，则 mu 已锁定
	 * - 如果 readers === 0 且 mu 已锁定，则写入器持有锁
	 */
	private readers = 0;
	private mu = new Mutex();

	public async withRead<T>(fn: () => Promise<T>): Promise<T> {
		while (this.readers === 0) {
			if (this.mu.isLocked()) {
				// 如果此时mu已锁定，则必须由写入器持有
				// 在这种情况下，我们旋转等待而不是尝试获取锁
				await new Promise(resolve => setTimeout(resolve, 100));
				continue;
			}
			// 没有读取器或写入器：为读取器获取锁
			await this.mu.acquire();
			break;
		}
		this.readers++;
		try {
			return await fn();
		} finally {
			this.readers--;
			if (this.readers === 0) {
				this.mu.release();
			}
		}
	}

	public async withWrite<T>(fn: () => Promise<T>): Promise<T> {
		return this.mu.runExclusive(fn);
	}
}
