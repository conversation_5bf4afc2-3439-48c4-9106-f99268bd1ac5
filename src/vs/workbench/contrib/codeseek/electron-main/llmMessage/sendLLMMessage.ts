/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { SendLLMMessageParams, OnText, OnFinalMessage, OnError, OnToolCall } from '../../common/llmMessageTypes.js';
import { IMetricsService } from '../../common/metricsService.js';
import { displayInfoOfProviderName } from '../../common/codeseekSettingsTypes.js';
import { sendLLMMessageToProviderImplementation } from './llmProxy.js';
import { ICodeseekLogger } from '../../common/codeseekLogService.js';
import { env, IdeTestConfig } from '../utils/common.js';


/**
 * Sends a language model (LLM) message with support for different message types and providers.
 *
 * @param params Configuration parameters for sending an LLM message, including message type, content, and callbacks
 * @param metricsService Service for capturing metrics about the LLM message interaction
 * @returns A promise that resolves when the message sending process is complete
 *
 * @remarks
 * Supports chat messages, agent messages, and Fill-in-the-Middle (FIM) messages across different providers.
 * Handles message streaming, tool calls, error handling, and provides abort functionality.
 */
export const sendLLMMessage = ({
	requestId,
	messagesType,
	codeseekSettingsState,
	messages: messages_,
	onText: onText_,
	onToolCall: onToolCall_,
	onFinalMessage: onFinalMessage_,
	onError: onError_,
	abortRef: abortRef_,
	logging: { loggingName },
	providerName,
	modelName,
	tools,
	agentParamsFromPlugin,
	llmChannel,
	agentParamsFromIde,
}: SendLLMMessageParams,
	metricsService: IMetricsService,
	logger: ICodeseekLogger
) => {
	if (env.AI_IDE_TEST_CONFIG) {
		const config: IdeTestConfig = JSON.parse(env.AI_IDE_TEST_CONFIG);
		if (config.llm) {
			if (config.llm.provider) {
				providerName = config.llm.provider;
			}
			if (config.llm.baseURL) {
				codeseekSettingsState.settingsOfProvider[providerName].baseURL = config.llm.baseURL;
			}
			if (config.llm.modelName) {
				modelName = config.llm.modelName;
			}
			if (config.llm.apiKey) {
				codeseekSettingsState.settingsOfProvider[providerName].apiKey = config.llm.apiKey;
			}
		}
	}
	// only captures number of messages and message "shape", no actual code, instructions, prompts, etc
	// const captureLLMEvent = (eventId: string, extras?: object) => {
	// 	metricsService.capture(eventId, {
	// 		providerName,
	// 		modelName,
	// 		customEndpointURL: codeseekSettingsState.settingsOfProvider[providerName]?.endpoint,
	// 		numModelsAtEndpoint: codeseekSettingsState.settingsOfProvider[providerName]?.models?.length,
	// 		...messagesType === 'chatMessages' ? {
	// 			numMessages: messages_?.length,
	// 			messagesShape: messages_?.map(msg => ({ role: msg.role, length: msg.content.length })),
	// 			origNumMessages: messages_?.length,
	// 			origMessagesShape: messages_?.map(msg => ({ role: msg.role, length: msg.content.length })),

	// 		} : messagesType === 'FIMMessage' ? {
	// 			prefixLength: messages_.prefix.length,
	// 			suffixLength: messages_.suffix.length,
	// 		} : {},

	// 		...extras,
	// 	});
	// };
	// const submit_time = new Date();

	// let _fullTextSoFar = '';
	let _aborter: (() => void) | null = null;
	const _setAborter = (fn: () => void) => { _aborter = fn; };
	let _didAbort = false;

	const onText: OnText = (params) => {
		// const { fullText } = params;
		if (_didAbort) return;
		onText_(params);
		// _fullTextSoFar = fullText;
	};

	const onToolCall: OnToolCall = (params) => {
		if (_didAbort) return;
		// captureLLMEvent(`${loggingName} - Executed Tool`, { toolName: params.toolCall.name, duration: new Date().getMilliseconds() - submit_time.getMilliseconds() });
		if (onToolCall_) {
			onToolCall_(params);
		}
	};

	const onFinalMessage: OnFinalMessage = ({ fullText, toolCalls }) => {
		if (_didAbort) return;
		// captureLLMEvent(`${loggingName} - Received Full Message`, { messageLength: fullText.length, duration: new Date().getMilliseconds() - submit_time.getMilliseconds() });
		onFinalMessage_({ fullText, toolCalls });
	};

	const onError: OnError = ({ message: error, fullError }) => {
		if (_didAbort) return;
		console.error('sendLLMMessage onError:', error);

		// handle failed to fetch errors, which give 0 information by design
		if (error === 'TypeError: fetch failed')
			error = `Failed to fetch from ${displayInfoOfProviderName(providerName).title}. This likely means you specified the wrong endpoint in Flow Settings, or your local model provider like Ollama is powered off.`;

		// captureLLMEvent(`${loggingName} - Error`, { error });
		onError_({ message: error, fullError });
	};

	const onAbort = () => {
		// captureLLMEvent(`${loggingName} - Abort`, { messageLengthSoFar: _fullTextSoFar.length });
		try { _aborter?.(); } // aborter sometimes automatically throws an error
		catch (e) { }
		_didAbort = true;
	};
	abortRef_.current = onAbort;

	// if (messagesType === 'chatMessages')
	// 	captureLLMEvent(`${loggingName} - Sending Message`, { messageLength: messages_[messages_.length - 1]?.content.length });
	// else if (messagesType === 'FIMMessage')
	// 	captureLLMEvent(`${loggingName} - Sending FIM`, {}); // TODO!!! add more metrics
	// else if (messagesType === 'agentMessages')
	// 	captureLLMEvent(`${loggingName} - Sending Agent Message`, { messageLength: messages_[messages_.length - 1]?.content.length });


	try {
		const implementation = sendLLMMessageToProviderImplementation[providerName as keyof typeof sendLLMMessageToProviderImplementation];
		if (!implementation) {
			onError({ message: `Error: Provider "${providerName}" not recognized.`, fullError: null });
			return;
		}
		const { sendChat, sendChatInAgentMode } = implementation;
		const sendFIM = implementation.sendFIM as unknown as ((params: any) => void) | null;
		if (messagesType === 'chatMessages') {
			sendChat({ messages: Array.isArray(messages_) ? messages_ : [], logger, onText, onToolCall, onFinalMessage, onError, codeseekSettingsState, modelName, _setAborter, providerName, tools });
			return;
		}
		if (messagesType === 'agentMessages') {
			sendChatInAgentMode({
				messages: Array.isArray(messages_) ? messages_ : [],
				onText,
				onToolCall,
				onFinalMessage,
				onError,
				codeseekSettingsState,
				modelName,
				_setAborter,
				providerName,
				tools,
				requestId,
				agentParamsFromPlugin,
				llmChannel,
				agentParamsFromIde,
				logger,
			});
			return;
		}
		if (messagesType === 'FIMMessage') {
			if (sendFIM) {
				sendFIM({ messages: !Array.isArray(messages_) ? messages_ : { prefix: '', suffix: '' }, onText, onToolCall, onFinalMessage, onError, codeseekSettingsState, modelName, _setAborter, providerName });
				return;
			}
			logger.error(`Error: This provider does not support Autocomplete yet.`);
			onError({ message: `Error: This provider does not support Autocomplete yet.`, fullError: null });
			return;
		}
		logger.error(`Error: Message type "${messagesType}" not recognized.`);
		onError({ message: `Error: Message type "${messagesType}" not recognized.`, fullError: null });
	} catch (error) {
		if (error instanceof Error) { onError({ message: error + '', fullError: error }); }
		else { onError({ message: `Unexpected Error in sendLLMMessage: ${error}`, fullError: error }); }
		// ; (_aborter as any)?.()
		// _didAbort = true
	}
};
