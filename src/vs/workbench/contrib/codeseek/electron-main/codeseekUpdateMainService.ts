/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import path from 'path';
import os from 'os';
import fs from 'fs/promises';
import * as https from "https";
import { spawn } from 'child_process';
import { Disposable } from '../../../../base/common/lifecycle.js';
import { IEnvironmentMainService } from '../../../../platform/environment/electron-main/environmentMainService.js';
import { IProductService } from '../../../../platform/product/common/productService.js';
import { ICodeseekUpdateService, UpgradeProgress } from '../common/codeseekUpdateService.js';
import { ICodeseekLogger } from '../common/codeseekLogService.js';
import { Emitter, Event } from '../../../../base/common/event.js';
import { IUacLoginService } from './uac/uacLoginService.js';

const platformPath = process.platform === "win32" ? path.win32 : path.posix;

// 类型定义
interface VersionInfo {
	latestVersion: string;
	hasUpdate: boolean;
	message?: string;
}

interface DownloadInfo {
	url: string;
	filePath: string;
	fileName: string;
}

// 错误类型
class UpdateError extends Error {
	constructor(message: string, public readonly code: string) {
		super(message);
		this.name = 'UpdateError';
	}
}

export class CodeseekMainUpdateService extends Disposable implements ICodeseekUpdateService {
	_serviceBrand: undefined;
	private readonly _onProgress = new Emitter<UpgradeProgress>();
	readonly onProgress: Event<UpgradeProgress> = this._onProgress.event;
	private _userInfo: any = null;
	private readonly _rootUserIds = [
		"10327829", "10240351", "10239990", "10340171", "10336933",
		"10144161", "10288547", "10226591", "10164355", "10200902"
	];
	private readonly _maxRetries = 10;
	private readonly _retryInterval = 5000;

	private _initialized = false;
	private _initPromise: Promise<void> | null = null;

	constructor(
		@IProductService private readonly _productService: IProductService,
		@IEnvironmentMainService private readonly _envMainService: IEnvironmentMainService,
		@ICodeseekLogger private readonly _logger: ICodeseekLogger,
		@IUacLoginService private readonly _uacLoginService: IUacLoginService
	) {
		super();
	}

	async init(): Promise<void> {
		if (this._initialized) return;
		if (this._initPromise) {
			return this._initPromise;
		}

		this._initPromise = (async () => {
			let attempts = 0;
			while (attempts < this._maxRetries) {
				const info = await this._uacLoginService.getUserInfo();
				if (info && info.userId) {
					this._userInfo = info;
					this._logger.info(`用户信息获取成功: ${info.userId}`);
					this._initialized = true;
					return;
				}
				attempts++;
				if (attempts >= this._maxRetries) {
					this._logger.error(`获取用户信息失败，已重试 ${this._maxRetries} 次`);
					return;
				}
				await new Promise(resolve => setTimeout(resolve, this._retryInterval));
			}
		})();

		return this._initPromise;
	}

	// 公共接口实现
	async check() {
		if (!this._initialized) {
			await this.init();
		}

		if (this._isDevMode()) {
			return { hasUpdate: false } as const;
		}

		if (!this._userInfo || !this._userInfo.userId) {
			this._logger.error('用户信息获取失败，无法检查更新');
			return null;
		}

		const userId = this._userInfo.userId.trim();
		if (this._rootUserIds.includes(userId)) {
			try {
				const versionInfo = await this._getVersionInfo();
				if (!versionInfo) {
					return null;
				}
				this._logger.info(versionInfo.latestVersion);
				return versionInfo.hasUpdate
					? {
						hasUpdate: true as const,
						message: versionInfo.message!,
						lastVersion: versionInfo.latestVersion,
					}
					: { hasUpdate: false } as const;
			} catch (error) {
				this._logger.error('检查更新失败:', error);
				return null;
			}
		} else {
			try {
				const latestIDEInfoFromPluginCenter = await this._fetchLatestIDEInfoFromPluginCenter(this._userInfo.userId.trim());
				const latestIDEVersion = this._extractVersion(latestIDEInfoFromPluginCenter.version)
				if (latestIDEVersion) {
					this._logger.info(latestIDEVersion);
					const currentVersion = this._productService.productVersion;
					// const currentVersion = '0.2.1'
					const hasUpdate = this._compareVersions(currentVersion, latestIDEVersion) < 0;

					return hasUpdate
						? {
							hasUpdate: true as const,
							message: hasUpdate ? `发现新版本 ${latestIDEVersion}，当前版本 ${currentVersion}` : '',
							lastVersion: latestIDEVersion,
						}
						: { hasUpdate: false } as const;;
				} else {
					return null;
				}

			} catch (error) {
				return null;
			}


		}
	}

	async install(): Promise<void> {
		if (this._rootUserIds.includes(this._userInfo.userId.trim())) {
			if (this._isDevMode()) {
				// return;
			}

			try {
				const versionInfo = await this._getVersionInfo();
				if (!versionInfo) return
				const downloadInfo = await this._prepareDownload(versionInfo);
				await this._download(downloadInfo.url, downloadInfo.filePath);
				await this._install(downloadInfo.filePath);
			} catch (error) {
				this._logger.error('更新失败:', error);
				throw error;
			}
		} else {
			try {
				const latestIDEInfoFromPluginCenter = await this._fetchLatestIDEInfoFromPluginCenter(this._userInfo.userId.trim());
				const latestIDEVersion = this._extractVersion(latestIDEInfoFromPluginCenter.version);
				const downloadUrl = latestIDEInfoFromPluginCenter.fileUrl;
				if (latestIDEVersion && downloadUrl) {

					const dataFolderName = this._productService.dataFolderName;
					const dataFolderPath = platformPath.join(os.homedir(), dataFolderName!);
					await this._ensureDirectoryExists(dataFolderPath);

					const name = this._productService.applicationName;
					const { fileName } = this._getFlowFileNameForSystem(name, latestIDEVersion);
					const filePath = platformPath.join(dataFolderPath, fileName);

					await this._download(downloadUrl, filePath);
					await this._install(filePath);
				}
			} catch (error) {
				this._logger.error('更新失败:', error);
				throw error;
			}
		}
	}

	// 私有辅助方法
	private _isDevMode(): boolean {
		return !this._envMainService.isBuilt;
	}

	private async _prepareDownload(versionInfo: VersionInfo): Promise<DownloadInfo> {
		const dataFolderName = this._productService.dataFolderName;
		const dataFolderPath = path.join(os.homedir(), dataFolderName!);
		await this._ensureDirectoryExists(dataFolderPath);

		const name = this._productService.applicationName;
		const { system, fileName } = this._getFlowFileNameForSystem(name, versionInfo.latestVersion);
		const filePath = path.join(dataFolderPath, fileName);
		const url = `${this._productService.downloadUrl}/${versionInfo.latestVersion}/${system}/${fileName}`;

		return { url, filePath, fileName };
	}

	private _getFlowFileNameForSystem(name: string, version: string): { system: string; fileName: string } {
		switch (process.platform) {
			case 'linux':
				return {
					system: 'linux',
					fileName: `${name}-${version}-1.x86_64.rpm`
				};
			case 'win32':
				return {
					system: 'windows',
					fileName: `${name.charAt(0).toUpperCase() + name.slice(1)}UserSetup-x64-${version}.exe`
				};
			default:
				throw new UpdateError(`不支持的操作系统: ${process.platform}`, 'UNSUPPORTED_PLATFORM');
		}
	}


	private async _getVersionInfo(): Promise<VersionInfo | null> {
		const res = await fetch(this._productService.downloadUrl!);
		const html = await res.text();
		const versions = this._extractVersions(html);

		if (versions.length === 0) {
			throw new UpdateError('未找到可用版本', 'NO_VERSION_FOUND');
		}

		const latestVersion = this._getLatestVersion(versions);
		const currentVersion = this._productService.productVersion;
		// const currentVersion = '0.2.1'
		const hasUpdate = this._compareVersions(currentVersion, latestVersion) < 0;

		return {
			latestVersion,
			hasUpdate,
			message: hasUpdate ? `发现新版本 ${latestVersion}，当前版本 ${currentVersion}` : ''
		};
	}

	private _fetchLatestIDEInfoFromPluginCenter(
		userId: string
	): Promise<{ version: string; fileUrl: string }> {
		return new Promise((resolve, reject) => {
			const options = {
				hostname: "ikan.zx.zte.com.cn",
				path: "/zideboot/api/ide/list/latest",
				method: "GET",
				headers: {
					"X-Access-Token": userId,
				},
			};
			const req = https.request(options, (res) => {
				let data = "";
				res.on("data", (chunk) => {
					data += chunk;
				});
				res.on("end", () => {
					try {
						const json = JSON.parse(data);
						if (json.success && json.result?.version && json.result?.fileUrl) {
							resolve({
								version: json.result.version,
								fileUrl: json.result.fileUrl,
							});
						} else {
							reject(new Error("无效的响应格式或数据缺失"));
						}
					} catch (err) {
						reject(err);
					}
				});
			});
			req.on("error", (e) => {
				reject(e);
			});
			req.end();
		});
	}

	private _extractVersion(str: string): string | null {
		const match = str.match(/-(\d+\.\d+\.\d+)-/);
		return match ? match[1] : null;
	}

	private _extractVersions(html: string): string[] {
		const versionRegex = /<a href="(\d+\.\d+\.\d+)\/">/g;
		const versions: string[] = [];
		let match;

		while ((match = versionRegex.exec(html)) !== null) {
			versions.push(match[1]);
		}

		return versions;
	}

	private _getLatestVersion(versions: string[]): string {
		return versions.sort((a, b) => this._compareVersions(b, a))[0];
	}

	private async _download(url: string, filePath: string): Promise<void> {
		try {
			this._logger.info(`开始下载: ${url}`);
			const response = await fetch(url);
			if (!response.ok) {
				throw new UpdateError(`下载失败: HTTP ${response.status}`, 'DOWNLOAD_FAILED');
			}

			const contentLength = response.headers.get('content-length');
			const totalBytes = contentLength ? parseInt(contentLength, 10) : 0;
			let receivedBytes = 0;

			const reader = response.body?.getReader();
			if (!reader) {
				throw new UpdateError('无法获取响应流', 'STREAM_ERROR');
			}

			const chunks: Uint8Array[] = [];
			while (true) {
				const { done, value } = await reader.read();
				if (done) break;

				chunks.push(value);
				receivedBytes += value.length;

				if (totalBytes > 0) {
					this._reportProgress('download', receivedBytes, totalBytes);
				}
			}

			const buffer = Buffer.concat(chunks);
			await fs.writeFile(filePath, buffer);
			this._logger.info(`下载完成: ${filePath}`);
		} catch (error) {
			this._logger.error(`下载文件失败: ${url}`, error);
			throw error;
		}
	}

	private async _install(filePath: string): Promise<void> {
		try {
			this._reportProgress('install', 0, 100, '准备安装更新...');

			if (process.platform === 'linux') {
				await this._spawnCommand('sudo', ['rpm', '-Uvh', filePath]);
			} else if (process.platform === 'win32') {
				await this._spawnCommand(filePath, [
					'/VERYSILENT',      // 完全静默安装，无界面显示
					'/SUPPRESSMSGBOXES', // 抑制所有消息框
					'/NORESTART',       // 不重启计算机
					'/CLOSEAPPLICATIONS', // 自动关闭正在运行的应用
					'/SP-',             // 禁用"这将安装..."的初始提示
					'/NOCANCEL'         // 隐藏取消按钮
				]);
			} else {
				throw new UpdateError(`不支持的操作系统: ${process.platform}`, 'UNSUPPORTED_PLATFORM');
			}
			this._reportProgress('install', 100, 100, '安装完成，准备重启...');
		} catch (error) {
			this._logger.error('安装失败:', error);
			throw error;
		} finally {
			await this._cleanupDownload(filePath);
		}
	}

	private _spawnCommand(command: string, args: string[]): Promise<void> {
		return new Promise((resolve, reject) => {
			// 对Windows添加特殊处理
			const options = process.platform === 'win32' ? { windowsVerbatimArguments: true } : undefined;

			const childProcess = spawn(command, args, options);
			let stdoutData = '';
			let stderrData = '';

			childProcess.stdout.on('data', (data) => {
				stdoutData += data.toString();
				this._logger.info('安装输出:', data.toString());
			});

			childProcess.stderr.on('data', (data) => {
				stderrData += data.toString();
				this._logger.warn('安装警告:', data.toString());
			});

			childProcess.on('error', (error) => {
				reject(error);
			});

			childProcess.on('close', (code) => {
				if (code === 0) {
					this._logger.info('安装命令执行成功');
					resolve();
				} else {
					reject(new Error(`命令执行失败，退出码: ${code}, 错误: ${stderrData}`));
				}
			});
		});
	}

	private async _cleanupDownload(filePath: string): Promise<void> {
		try {
			this._logger.info('清理下载文件:', filePath);
			await fs.rm(filePath, { force: true });
		} catch (error) {
			this._logger.warn('清理文件失败:', error);
		}
	}

	private async _ensureDirectoryExists(dirPath: string): Promise<void> {
		try {
			await fs.access(dirPath);
		} catch {
			await fs.mkdir(dirPath, { recursive: true });
		}
	}

	private _compareVersions(v1: string, v2: string): number {
		const [v1Major, v1Minor, v1Patch] = v1.split('.').map(Number);
		const [v2Major, v2Minor, v2Patch] = v2.split('.').map(Number);

		if (v1Major !== v2Major) return v1Major - v2Major;
		if (v1Minor !== v2Minor) return v1Minor - v2Minor;
		return v1Patch - v2Patch;
	}

	private _reportProgress(stage: 'download' | 'install', received: number, total: number, message?: string): void {
		const percentage = Math.round((received / total) * 100);
		this._onProgress.fire({
			receivedBytes: received,
			totalBytes: total,
			percentage,
			stage,
			message: message || (stage === 'download' ? `正在下载更新包: ${percentage}%` : '')
		});
	}
}

